import 'package:billionaires_social/core/providers/initialization_provider.dart';
import 'package:billionaires_social/core/app_themes.dart';
import 'package:billionaires_social/features/settings/providers/theme_provider.dart';
import 'package:flutter/material.dart';
import 'package:billionaires_social/core/widgets/auth_wrapper.dart';
import 'package:billionaires_social/core/service_locator.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:billionaires_social/core/services/universal_account_service.dart';
import 'package:billionaires_social/core/services/user_data_consistency_service.dart';
import 'package:billionaires_social/core/services/version_control_service.dart';
import 'package:billionaires_social/core/services/performance_monitoring_service.dart';
import 'package:billionaires_social/core/services/memory_management_service.dart';
import 'package:billionaires_social/core/security/certificate_pinning_service.dart';
import 'package:billionaires_social/features/onboarding/screens/beta_tester_onboarding_screen.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:firebase_core/firebase_core.dart';

void main() async {
  FlutterError.onError = (FlutterErrorDetails details) {
    FlutterError.presentError(details);
    debugPrint('FLUTTER ERROR: ${details.exceptionAsString()}');
    if (details.stack != null) debugPrint(details.stack.toString());
  };
  WidgetsFlutterBinding.ensureInitialized();
  debugPrint('Before Firebase init');
  await Firebase.initializeApp();
  debugPrint('After Firebase init');
  await setupServiceLocator();
  debugPrint('After service locator');

  // Initialize security services
  debugPrint('Initializing security services...');
  await CertificatePinningService().initialize();
  debugPrint('✅ Certificate pinning service initialized');

  // Ensure current user has universal defaults
  await UniversalAccountService.checkCurrentUser();

  // Validate and fix user data consistency
  await UserDataConsistencyService.validateCurrentUserDataConsistency();

  // Start performance monitoring
  await PerformanceMonitoringService().startMonitoring();

  // Start memory management monitoring
  await getIt<MemoryManagementService>().startMonitoring();

  runApp(const ProviderScope(child: MyApp()));
}

class MyApp extends ConsumerWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentTheme = ref.watch(themeProvider);

    return MaterialApp(
      title: 'Billionaires Social',
      theme: AppThemes.getTheme(currentTheme),
      darkTheme: AppThemes.getTheme(
        AppThemeType.luxuryWhiteGold,
      ), // Always use white theme for dark mode
      themeMode: ThemeMode.light, // Force light theme
      home: const AppFlowManager(), // Directly to AppFlowManager
    );
  }
}

class AppFlowManager extends ConsumerWidget {
  const AppFlowManager({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final appStatus = ref.watch(initializationProvider);
    debugPrint('AppFlowManager appStatus: $appStatus');
    return appStatus.when(
      data: (status) {
        debugPrint('AppFlowManager status: $status');
        switch (status) {
          case AppStatus.forceUpdate:
            return _buildForceUpdateDialog(context);
          case AppStatus.onboarding:
            return BetaTesterOnboardingScreen(
              onComplete: () async {
                final prefs = await SharedPreferences.getInstance();
                await prefs.setBool('beta_onboarding_completed', true);
                ref.invalidate(initializationProvider);
              },
            );
          case AppStatus.authenticated:
            return const AuthWrapper();
          default:
            return const LaunchSplashScreen();
        }
      },
      loading: () => const LaunchSplashScreen(),
      error: (err, stack) {
        debugPrint('AppFlowManager error: $err');
        return Scaffold(
          body: Center(child: Text('Initialization Error: $err')),
        );
      },
    );
  }

  // Moved _buildForceUpdateDialog here, making it a static method or a standalone widget
  Widget _buildForceUpdateDialog(BuildContext context) {
    final versionService = getIt<VersionControlService>();
    final latestVersion = versionService.getLatestVersion();

    return Scaffold(
      backgroundColor: Colors.black87,
      body: Center(
        child: Container(
          margin: const EdgeInsets.all(32),
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.3),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(Icons.system_update, size: 64, color: Colors.blue),
              const SizedBox(height: 16),
              const Text(
                'Update Required',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),
              Text(
                latestVersion?.updateMessage ??
                    'A new version is available and required to continue using the app.',
                textAlign: TextAlign.center,
                style: const TextStyle(fontSize: 16),
              ),
              const SizedBox(height: 24),
              if (latestVersion?.changelog != null) ...[
                const Text(
                  'What\'s New:',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                ...latestVersion!.changelog!
                    .take(3)
                    .map(
                      (item) => Padding(
                        padding: const EdgeInsets.symmetric(vertical: 2),
                        child: Row(
                          children: [
                            const Icon(
                              Icons.check,
                              size: 16,
                              color: Colors.green,
                            ),
                            const SizedBox(width: 8),
                            Expanded(child: Text(item)),
                          ],
                        ),
                      ),
                    ),
                const SizedBox(height: 24),
              ],
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () => versionService.launchAppStore(),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text(
                    'Update Now',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class LaunchSplashScreen extends StatelessWidget {
  const LaunchSplashScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Initializing...'),
          ],
        ),
      ),
    );
  }
}
