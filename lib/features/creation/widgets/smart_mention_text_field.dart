import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:billionaires_social/features/search/services/user_search_service.dart';
import 'package:billionaires_social/features/profile/models/profile_model.dart';

class SmartMentionTextField extends ConsumerStatefulWidget {
  final TextEditingController controller;
  final String hintText;
  final int maxLines;
  final Function(List<String>)? onMentionsChanged;
  final Function(List<String>)? onHashtagsChanged;
  final TextStyle? style;
  final InputDecoration? decoration;

  const SmartMentionTextField({
    super.key,
    required this.controller,
    this.hintText = 'What\'s on your mind?',
    this.maxLines = 5,
    this.onMentionsChanged,
    this.onHashtagsChanged,
    this.style,
    this.decoration,
  });

  @override
  ConsumerState<SmartMentionTextField> createState() =>
      _SmartMentionTextFieldState();
}

class _SmartMentionTextFieldState extends ConsumerState<SmartMentionTextField> {
  final FocusNode _focusNode = FocusNode();
  final LayerLink _layerLink = LayerLink();
  OverlayEntry? _overlayEntry;

  List<ProfileModel> _suggestions = [];
  bool _showSuggestions = false;
  int _mentionStartIndex = -1;

  // Track mentions and hashtags
  final Set<String> _mentions = {};
  final Set<String> _hashtags = {};

  @override
  void initState() {
    super.initState();
    widget.controller.addListener(_onTextChanged);
    _focusNode.addListener(_onFocusChanged);
  }

  @override
  void dispose() {
    widget.controller.removeListener(_onTextChanged);
    _focusNode.removeListener(_onFocusChanged);
    _focusNode.dispose();
    _removeOverlay();
    super.dispose();
  }

  void _onFocusChanged() {
    if (!_focusNode.hasFocus) {
      _hideSuggestions();
    }
  }

  void _onTextChanged() {
    final text = widget.controller.text;
    final cursorPosition = widget.controller.selection.baseOffset;

    // Extract mentions and hashtags
    _extractMentionsAndHashtags(text);

    // Check for @ mention
    _checkForMention(text, cursorPosition);
  }

  void _extractMentionsAndHashtags(String text) {
    // Extract mentions (@username)
    final mentionRegex = RegExp(r'@(\w+)');
    final mentionMatches = mentionRegex.allMatches(text);
    final newMentions = mentionMatches.map((match) => match.group(1)!).toSet();

    // Extract hashtags (#hashtag)
    final hashtagRegex = RegExp(r'#(\w+)');
    final hashtagMatches = hashtagRegex.allMatches(text);
    final newHashtags = hashtagMatches.map((match) => match.group(1)!).toSet();

    // Update mentions and hashtags if changed
    if (!_mentions.containsAll(newMentions) ||
        !newMentions.containsAll(_mentions)) {
      _mentions.clear();
      _mentions.addAll(newMentions);
      widget.onMentionsChanged?.call(_mentions.toList());
    }

    if (!_hashtags.containsAll(newHashtags) ||
        !newHashtags.containsAll(_hashtags)) {
      _hashtags.clear();
      _hashtags.addAll(newHashtags);
      widget.onHashtagsChanged?.call(_hashtags.toList());
    }
  }

  void _checkForMention(String text, int cursorPosition) {
    if (cursorPosition <= 0) {
      _hideSuggestions();
      return;
    }

    // Find the last @ before cursor
    int atIndex = -1;
    for (int i = cursorPosition - 1; i >= 0; i--) {
      if (text[i] == '@') {
        atIndex = i;
        break;
      } else if (text[i] == ' ' || text[i] == '\n') {
        break;
      }
    }

    if (atIndex == -1) {
      _hideSuggestions();
      return;
    }

    // Check if @ is at start or preceded by whitespace
    if (atIndex > 0 && text[atIndex - 1] != ' ' && text[atIndex - 1] != '\n') {
      _hideSuggestions();
      return;
    }

    // Extract query after @
    final query = text.substring(atIndex + 1, cursorPosition);

    // Check if query contains spaces (invalid mention)
    if (query.contains(' ') || query.contains('\n')) {
      _hideSuggestions();
      return;
    }

    _mentionStartIndex = atIndex;
    _searchUsers(query);
  }

  Future<void> _searchUsers(String query) async {
    if (query.isEmpty) {
      // Show recent/suggested users when no query
      _showRecentSuggestions();
      return;
    }

    try {
      final userSearchService = UserSearchService();

      final results = await userSearchService.searchUsers(
        query: query,
        limit: 8,
      );

      if (mounted) {
        setState(() {
          _suggestions = results;
          _showSuggestions = results.isNotEmpty;
        });

        if (_showSuggestions) {
          _showSuggestionsOverlay();
        } else {
          _hideSuggestions();
        }
      }
    } catch (e) {
      debugPrint('Error searching users for mentions: $e');
      _hideSuggestions();
    }
  }

  Future<void> _showRecentSuggestions() async {
    try {
      final userSearchService = UserSearchService();

      // Get suggested users (simplified - just search for popular users)
      final recent = await userSearchService.searchUsers(query: '', limit: 5);

      if (mounted) {
        setState(() {
          _suggestions = recent;
          _showSuggestions = recent.isNotEmpty;
        });

        if (_showSuggestions) {
          _showSuggestionsOverlay();
        }
      }
    } catch (e) {
      debugPrint('Error loading recent suggestions: $e');
    }
  }

  void _showSuggestionsOverlay() {
    _removeOverlay();

    _overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        width: MediaQuery.of(context).size.width - 32,
        child: CompositedTransformFollower(
          link: _layerLink,
          showWhenUnlinked: false,
          offset: const Offset(0, 40),
          child: Material(
            elevation: 8,
            borderRadius: BorderRadius.circular(12),
            child: Container(
              constraints: const BoxConstraints(maxHeight: 200),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: ListView.builder(
                shrinkWrap: true,
                padding: EdgeInsets.zero,
                itemCount: _suggestions.length,
                itemBuilder: (context, index) {
                  final user = _suggestions[index];
                  return _buildSuggestionTile(user);
                },
              ),
            ),
          ),
        ),
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
  }

  Widget _buildSuggestionTile(ProfileModel user) {
    return ListTile(
      dense: true,
      leading: CircleAvatar(
        radius: 16,
        backgroundImage: user.profilePictureUrl.isNotEmpty
            ? CachedNetworkImageProvider(user.profilePictureUrl)
            : null,
        child: user.profilePictureUrl.isEmpty
            ? const Icon(Icons.person, size: 16)
            : null,
      ),
      title: Row(
        children: [
          Expanded(
            child: Text(
              user.name.isNotEmpty ? user.name : user.username,
              style: const TextStyle(fontWeight: FontWeight.w600, fontSize: 14),
              overflow: TextOverflow.ellipsis,
            ),
          ),
          if (user.isVerified) ...[
            const SizedBox(width: 4),
            const Icon(Icons.verified, color: Colors.blue, size: 14),
          ],
        ],
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '@${user.username}',
            style: TextStyle(color: Colors.grey[600], fontSize: 12),
          ),
          if (user.followerCount > 0)
            Text(
              '${_formatCount(user.followerCount)} followers',
              style: TextStyle(color: Colors.grey[500], fontSize: 11),
            ),
        ],
      ),
      onTap: () => _selectUser(user),
    );
  }

  void _selectUser(ProfileModel user) {
    final text = widget.controller.text;
    final beforeMention = text.substring(0, _mentionStartIndex);
    final afterCursor = text.substring(widget.controller.selection.baseOffset);

    final newText = '$beforeMention@${user.username} $afterCursor';
    final newCursorPosition = beforeMention.length + user.username.length + 2;

    widget.controller.text = newText;
    widget.controller.selection = TextSelection.collapsed(
      offset: newCursorPosition,
    );

    _hideSuggestions();

    // Save to search history
    _saveToSearchHistory(user);
  }

  Future<void> _saveToSearchHistory(ProfileModel user) async {
    try {
      // Simplified - just log the search for now
      debugPrint('User mentioned: ${user.username}');
    } catch (e) {
      debugPrint('Error saving to search history: $e');
    }
  }

  void _hideSuggestions() {
    setState(() {
      _showSuggestions = false;
    });
    _removeOverlay();
  }

  void _removeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  String _formatCount(int count) {
    if (count >= 1000000) {
      return '${(count / 1000000).toStringAsFixed(1)}M';
    } else if (count >= 1000) {
      return '${(count / 1000).toStringAsFixed(1)}K';
    }
    return count.toString();
  }

  @override
  Widget build(BuildContext context) {
    return CompositedTransformTarget(
      link: _layerLink,
      child: TextField(
        controller: widget.controller,
        focusNode: _focusNode,
        maxLines: widget.maxLines,
        style: widget.style ?? const TextStyle(fontSize: 16),
        decoration:
            widget.decoration ??
            InputDecoration(
              hintText: widget.hintText,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              contentPadding: const EdgeInsets.all(16),
            ),
      ),
    );
  }
}
