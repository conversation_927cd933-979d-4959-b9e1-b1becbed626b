import 'dart:io';
import 'dart:typed_data';
import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:image/image.dart' as img;
import 'package:path_provider/path_provider.dart';
import 'package:billionaires_social/features/creation/screens/text_overlay_screen.dart';
import 'package:billionaires_social/features/creation/screens/advanced_crop_screen.dart';

class ImageEditingScreen extends StatefulWidget {
  final File imageFile;
  final int imageIndex;
  final int totalImages;

  const ImageEditingScreen({
    super.key,
    required this.imageFile,
    required this.imageIndex,
    required this.totalImages,
  });

  @override
  State<ImageEditingScreen> createState() => _ImageEditingScreenState();
}

class _ImageEditingScreenState extends State<ImageEditingScreen> {
  Uint8List? _imageData;
  bool _isLoading = true;
  bool _showFilters = false;

  // Filter values
  double _brightness = 0.0; // -100 to 100
  double _contrast = 0.0; // -100 to 100
  double _saturation = 0.0; // -100 to 100
  double _exposure = 0.0; // -100 to 100
  double _highlights = 0.0; // -100 to 100
  double _shadows = 0.0; // -100 to 100
  double _warmth = 0.0; // -100 to 100
  double _tint = 0.0; // -100 to 100
  double _sharpness = 0.0; // -100 to 100
  double _vignette = 0.0; // 0 to 100

  // Filter presets
  String _selectedFilter = 'none';
  final Map<String, Map<String, double>> _filterPresets = {
    'none': {},
    'vintage': {
      'brightness': 10,
      'contrast': 15,
      'saturation': -20,
      'warmth': 20,
      'vignette': 30,
    },
    'blackwhite': {'saturation': -100, 'contrast': 20, 'brightness': 5},
    'sepia': {
      'saturation': -50,
      'warmth': 40,
      'brightness': 10,
      'contrast': 10,
    },
    'dramatic': {
      'contrast': 40,
      'shadows': -30,
      'highlights': -20,
      'saturation': 20,
      'vignette': 20,
    },
    'bright': {
      'brightness': 25,
      'exposure': 15,
      'highlights': 20,
      'shadows': 30,
    },
    'moody': {
      'brightness': -15,
      'contrast': 25,
      'shadows': -40,
      'saturation': -10,
      'vignette': 40,
    },
    'vivid': {
      'saturation': 40,
      'contrast': 20,
      'brightness': 10,
      'sharpness': 20,
    },
    'cool': {'warmth': -30, 'tint': -10, 'brightness': 5, 'contrast': 15},
    'warm': {'warmth': 30, 'tint': 10, 'brightness': 10, 'saturation': 10},
  };

  // Processed image data
  Uint8List? _processedImageData;
  File? _editedImageFile;

  @override
  void initState() {
    super.initState();
    _loadImage();
  }

  Future<void> _loadImage() async {
    try {
      final bytes = await widget.imageFile.readAsBytes();
      setState(() {
        _imageData = bytes;
        _processedImageData = bytes;
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('Error loading image: $e');
      setState(() => _isLoading = false);
    }
  }

  Future<void> _applyFilters() async {
    if (_imageData == null) return;

    setState(() => _isLoading = true);

    try {
      // Decode the image
      final image = img.decodeImage(_imageData!);
      if (image == null) return;

      // Apply filters
      img.Image processedImage = image;

      // Apply exposure (affects overall brightness)
      if (_exposure != 0) {
        processedImage = img.adjustColor(
          processedImage,
          brightness: 1.0 + (_exposure / 200), // More subtle than brightness
        );
      }

      // Apply brightness
      if (_brightness != 0) {
        processedImage = img.adjustColor(
          processedImage,
          brightness: 1.0 + (_brightness / 100),
        );
      }

      // Apply contrast
      if (_contrast != 0) {
        processedImage = img.adjustColor(
          processedImage,
          contrast: 1.0 + (_contrast / 100),
        );
      }

      // Apply saturation
      if (_saturation != 0) {
        processedImage = img.adjustColor(
          processedImage,
          saturation: 1.0 + (_saturation / 100),
        );
      }

      // Apply highlights and shadows (simplified implementation)
      if (_highlights != 0 || _shadows != 0) {
        processedImage = _adjustHighlightsAndShadows(
          processedImage,
          _highlights,
          _shadows,
        );
      }

      // Apply warmth and tint (color temperature adjustments)
      if (_warmth != 0 || _tint != 0) {
        processedImage = _adjustColorTemperature(
          processedImage,
          _warmth,
          _tint,
        );
      }

      // Apply sharpness
      if (_sharpness != 0) {
        processedImage = _applySharpen(processedImage, _sharpness);
      }

      // Apply vignette
      if (_vignette > 0) {
        processedImage = _applyVignette(processedImage, _vignette);
      }

      // Encode back to bytes
      final processedBytes = Uint8List.fromList(img.encodeJpg(processedImage));

      setState(() {
        _processedImageData = processedBytes;
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('Error applying filters: $e');
      setState(() => _isLoading = false);
    }
  }

  Future<void> _saveEditedImage() async {
    if (_processedImageData == null) return;

    try {
      final tempDir = await getTemporaryDirectory();
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final editedPath = '${tempDir.path}/edited_image_$timestamp.jpg';

      final editedFile = File(editedPath);
      await editedFile.writeAsBytes(_processedImageData!);

      setState(() {
        _editedImageFile = editedFile;
      });
    } catch (e) {
      debugPrint('Error saving edited image: $e');
    }
  }

  // Helper methods for advanced filters
  img.Image _adjustHighlightsAndShadows(
    img.Image image,
    double highlights,
    double shadows,
  ) {
    // Simplified highlights/shadows adjustment
    // In a real implementation, you'd analyze luminance and adjust accordingly
    final highlightFactor = 1.0 + (highlights / 200);
    final shadowFactor = 1.0 + (shadows / 200);

    return img.adjustColor(
      image,
      brightness: shadowFactor, // Affects darker areas more
      contrast: highlightFactor, // Affects brighter areas more
    );
  }

  img.Image _adjustColorTemperature(
    img.Image image,
    double warmth,
    double tint,
  ) {
    // Simplified color temperature adjustment using hue shift
    final warmthFactor = warmth / 100;
    final tintFactor = tint / 100;

    // Apply warmth as a hue shift and tint as saturation adjustment
    return img.adjustColor(
      image,
      hue: warmthFactor * 0.1, // Subtle hue shift for warmth
      saturation: 1.0 + (tintFactor * 0.2), // Tint affects saturation
    );
  }

  img.Image _applySharpen(img.Image image, double sharpness) {
    if (sharpness == 0) return image;

    // Simple sharpening using convolution
    final strength = sharpness / 100;
    return img.convolution(
      image,
      filter: [
        0,
        -strength,
        0,
        -strength,
        1 + (4 * strength),
        -strength,
        0,
        -strength,
        0,
      ],
      div: 1,
    );
  }

  img.Image _applyVignette(img.Image image, double vignette) {
    if (vignette == 0) return image;

    // Simplified vignette using brightness adjustment from center
    final strength = vignette / 100;
    final centerX = image.width / 2;
    final centerY = image.height / 2;
    final maxDistance = math.sqrt(centerX * centerX + centerY * centerY);

    for (int y = 0; y < image.height; y++) {
      for (int x = 0; x < image.width; x++) {
        final distance = math.sqrt(
          math.pow(x - centerX, 2) + math.pow(y - centerY, 2),
        );
        final factor = 1.0 - (distance / maxDistance * strength);
        final clampedFactor = math.max(
          0.2,
          factor,
        ); // Don't go completely black

        final pixel = image.getPixel(x, y);
        final r = (pixel.r * clampedFactor).round().clamp(0, 255);
        final g = (pixel.g * clampedFactor).round().clamp(0, 255);
        final b = (pixel.b * clampedFactor).round().clamp(0, 255);

        image.setPixel(x, y, img.ColorRgb8(r, g, b));
      }
    }

    return image;
  }

  void _applyFilterPreset(String filterName) {
    final preset = _filterPresets[filterName];
    if (preset == null) return;

    setState(() {
      _selectedFilter = filterName;
      _brightness = preset['brightness'] ?? 0.0;
      _contrast = preset['contrast'] ?? 0.0;
      _saturation = preset['saturation'] ?? 0.0;
      _exposure = preset['exposure'] ?? 0.0;
      _highlights = preset['highlights'] ?? 0.0;
      _shadows = preset['shadows'] ?? 0.0;
      _warmth = preset['warmth'] ?? 0.0;
      _tint = preset['tint'] ?? 0.0;
      _sharpness = preset['sharpness'] ?? 0.0;
      _vignette = preset['vignette'] ?? 0.0;
    });

    _applyFilters();
  }

  String _getFilterDisplayName(String filterName) {
    switch (filterName) {
      case 'none':
        return 'Original';
      case 'blackwhite':
        return 'B&W';
      case 'sepia':
        return 'Sepia';
      case 'vintage':
        return 'Vintage';
      case 'dramatic':
        return 'Dramatic';
      case 'bright':
        return 'Bright';
      case 'moody':
        return 'Moody';
      case 'vivid':
        return 'Vivid';
      case 'cool':
        return 'Cool';
      case 'warm':
        return 'Warm';
      default:
        return filterName.toUpperCase();
    }
  }

  Future<void> _addTextOverlay() async {
    try {
      // Capture context before async operations
      final navigator = Navigator.of(context);
      final scaffoldMessenger = ScaffoldMessenger.of(context);

      // Save current edited image first
      await _saveEditedImage();
      final currentFile = _editedImageFile ?? widget.imageFile;

      if (!mounted) return;

      final result = await navigator.push<File>(
        MaterialPageRoute(
          builder: (context) => TextOverlayScreen(imageFile: currentFile),
        ),
      );

      if (result != null && mounted) {
        // Update the current image with the text overlay result
        final bytes = await result.readAsBytes();
        setState(() {
          _processedImageData = bytes;
          _editedImageFile = result;
        });

        scaffoldMessenger.showSnackBar(
          const SnackBar(
            content: Text('Text overlay added successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      debugPrint('Error adding text overlay: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to add text overlay: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _cropImage() async {
    try {
      // Capture context before async operations
      final navigator = Navigator.of(context);
      final scaffoldMessenger = ScaffoldMessenger.of(context);

      // Save current edited image first
      await _saveEditedImage();
      final currentFile = _editedImageFile ?? widget.imageFile;

      if (!mounted) return;

      final result = await navigator.push<File>(
        MaterialPageRoute(
          builder: (context) => AdvancedCropScreen(imageFile: currentFile),
        ),
      );

      if (result != null && mounted) {
        // Update the current image with the cropped result
        final bytes = await result.readAsBytes();
        setState(() {
          _processedImageData = bytes;
          _editedImageFile = result;
        });

        scaffoldMessenger.showSnackBar(
          const SnackBar(
            content: Text('Image cropped successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      debugPrint('Error cropping image: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to crop image: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _resetToOriginal() {
    setState(() {
      _processedImageData = _imageData;
      _selectedFilter = 'none';
      _brightness = 0.0;
      _contrast = 0.0;
      _saturation = 0.0;
      _exposure = 0.0;
      _highlights = 0.0;
      _shadows = 0.0;
      _warmth = 0.0;
      _tint = 0.0;
      _sharpness = 0.0;
      _vignette = 0.0;
    });
  }

  void _finishEditing() async {
    await _saveEditedImage();
    if (mounted) {
      Navigator.of(context).pop(_editedImageFile ?? widget.imageFile);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
        title: Text(
          'Edit Image ${widget.imageIndex + 1}/${widget.totalImages}',
          style: const TextStyle(color: Colors.white),
        ),
        actions: [
          TextButton(
            onPressed: _finishEditing,
            child: const Text(
              'Done',
              style: TextStyle(color: Colors.blue, fontWeight: FontWeight.w600),
            ),
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // Image preview
                Expanded(
                  flex: 3,
                  child: Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(16),
                    child: _processedImageData != null
                        ? ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: Image.memory(
                              _processedImageData!,
                              fit: BoxFit.contain,
                              width: double.infinity,
                              height: double.infinity,
                            ),
                          )
                        : const Center(
                            child: Text(
                              'Failed to load image',
                              style: TextStyle(color: Colors.white),
                            ),
                          ),
                  ),
                ),

                // Controls
                Expanded(
                  flex: 2,
                  child: Container(
                    color: Colors.grey[900],
                    child: Column(
                      children: [
                        // Action buttons
                        Padding(
                          padding: const EdgeInsets.all(16),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            children: [
                              _buildActionButton(
                                icon: Icons.tune,
                                label: 'Filters',
                                onPressed: () {
                                  setState(() => _showFilters = !_showFilters);
                                },
                                isActive: _showFilters,
                              ),
                              _buildActionButton(
                                icon: Icons.text_fields,
                                label: 'Text',
                                onPressed: _addTextOverlay,
                              ),
                              _buildActionButton(
                                icon: Icons.crop,
                                label: 'Crop',
                                onPressed: _cropImage,
                              ),
                              _buildActionButton(
                                icon: Icons.refresh,
                                label: 'Reset',
                                onPressed: _resetToOriginal,
                              ),
                            ],
                          ),
                        ),

                        // Filter controls
                        if (_showFilters) ...[
                          const Divider(color: Colors.grey),

                          // Filter Presets
                          Container(
                            height: 100,
                            padding: const EdgeInsets.symmetric(vertical: 8),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Padding(
                                  padding: EdgeInsets.symmetric(horizontal: 16),
                                  child: Text(
                                    'Presets',
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 14,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                                const SizedBox(height: 8),
                                Expanded(
                                  child: ListView.builder(
                                    scrollDirection: Axis.horizontal,
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 16,
                                    ),
                                    itemCount: _filterPresets.keys.length,
                                    itemBuilder: (context, index) {
                                      final filterName = _filterPresets.keys
                                          .elementAt(index);
                                      final isSelected =
                                          _selectedFilter == filterName;

                                      return Padding(
                                        padding: const EdgeInsets.only(
                                          right: 12,
                                        ),
                                        child: GestureDetector(
                                          onTap: () =>
                                              _applyFilterPreset(filterName),
                                          child: Column(
                                            children: [
                                              Container(
                                                width: 50,
                                                height: 50,
                                                decoration: BoxDecoration(
                                                  borderRadius:
                                                      BorderRadius.circular(8),
                                                  border: Border.all(
                                                    color: isSelected
                                                        ? Colors.blue
                                                        : Colors.grey[600]!,
                                                    width: 2,
                                                  ),
                                                  color: Colors.grey[700],
                                                ),
                                                child: const Icon(
                                                  Icons.filter,
                                                  color: Colors.white,
                                                  size: 20,
                                                ),
                                              ),
                                              const SizedBox(height: 4),
                                              Text(
                                                _getFilterDisplayName(
                                                  filterName,
                                                ),
                                                style: TextStyle(
                                                  color: isSelected
                                                      ? Colors.blue
                                                      : Colors.white,
                                                  fontSize: 10,
                                                  fontWeight: isSelected
                                                      ? FontWeight.w600
                                                      : FontWeight.normal,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      );
                                    },
                                  ),
                                ),
                              ],
                            ),
                          ),

                          const Divider(color: Colors.grey),

                          // Manual Adjustments
                          Expanded(
                            child: SingleChildScrollView(
                              padding: const EdgeInsets.all(16),
                              child: Column(
                                children: [
                                  _buildSlider(
                                    'Brightness',
                                    _brightness,
                                    -100,
                                    100,
                                    (value) {
                                      setState(() => _brightness = value);
                                      _applyFilters();
                                    },
                                  ),
                                  _buildSlider(
                                    'Contrast',
                                    _contrast,
                                    -100,
                                    100,
                                    (value) {
                                      setState(() => _contrast = value);
                                      _applyFilters();
                                    },
                                  ),
                                  _buildSlider(
                                    'Saturation',
                                    _saturation,
                                    -100,
                                    100,
                                    (value) {
                                      setState(() => _saturation = value);
                                      _applyFilters();
                                    },
                                  ),
                                  _buildSlider(
                                    'Exposure',
                                    _exposure,
                                    -100,
                                    100,
                                    (value) {
                                      setState(() => _exposure = value);
                                      _applyFilters();
                                    },
                                  ),
                                  _buildSlider(
                                    'Highlights',
                                    _highlights,
                                    -100,
                                    100,
                                    (value) {
                                      setState(() => _highlights = value);
                                      _applyFilters();
                                    },
                                  ),
                                  _buildSlider('Shadows', _shadows, -100, 100, (
                                    value,
                                  ) {
                                    setState(() => _shadows = value);
                                    _applyFilters();
                                  }),
                                  _buildSlider('Warmth', _warmth, -100, 100, (
                                    value,
                                  ) {
                                    setState(() => _warmth = value);
                                    _applyFilters();
                                  }),
                                  _buildSlider(
                                    'Sharpness',
                                    _sharpness,
                                    -100,
                                    100,
                                    (value) {
                                      setState(() => _sharpness = value);
                                      _applyFilters();
                                    },
                                  ),
                                  _buildSlider('Vignette', _vignette, 0, 100, (
                                    value,
                                  ) {
                                    setState(() => _vignette = value);
                                    _applyFilters();
                                  }),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
              ],
            ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
    bool isLoading = false,
    bool isActive = false,
  }) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            color: isActive ? Colors.blue : Colors.grey[800],
            borderRadius: BorderRadius.circular(30),
          ),
          child: IconButton(
            onPressed: isLoading ? null : onPressed,
            icon: isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : Icon(icon, color: Colors.white),
          ),
        ),
        const SizedBox(height: 8),
        Text(label, style: const TextStyle(color: Colors.white, fontSize: 12)),
      ],
    );
  }

  Widget _buildSlider(
    String label,
    double value,
    double min,
    double max,
    ValueChanged<double> onChanged,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              label,
              style: const TextStyle(color: Colors.white, fontSize: 14),
            ),
            Text(
              value.round().toString(),
              style: const TextStyle(color: Colors.grey, fontSize: 12),
            ),
          ],
        ),
        Slider(
          value: value,
          min: min,
          max: max,
          divisions: 200,
          activeColor: Colors.blue,
          inactiveColor: Colors.grey[700],
          onChanged: onChanged,
        ),
      ],
    );
  }
}
