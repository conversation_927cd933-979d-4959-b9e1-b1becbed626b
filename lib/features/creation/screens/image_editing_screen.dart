import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:image/image.dart' as img;
import 'package:path_provider/path_provider.dart';

class ImageEditingScreen extends StatefulWidget {
  final File imageFile;
  final int imageIndex;
  final int totalImages;

  const ImageEditingScreen({
    super.key,
    required this.imageFile,
    required this.imageIndex,
    required this.totalImages,
  });

  @override
  State<ImageEditingScreen> createState() => _ImageEditingScreenState();
}

class _ImageEditingScreenState extends State<ImageEditingScreen> {
  Uint8List? _imageData;
  bool _isLoading = true;
  bool _showFilters = false;

  // Filter values
  double _brightness = 0.0; // -100 to 100
  double _contrast = 0.0; // -100 to 100
  double _saturation = 0.0; // -100 to 100

  // Processed image data
  Uint8List? _processedImageData;
  File? _editedImageFile;

  @override
  void initState() {
    super.initState();
    _loadImage();
  }

  Future<void> _loadImage() async {
    try {
      final bytes = await widget.imageFile.readAsBytes();
      setState(() {
        _imageData = bytes;
        _processedImageData = bytes;
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('Error loading image: $e');
      setState(() => _isLoading = false);
    }
  }

  Future<void> _applyFilters() async {
    if (_imageData == null) return;

    setState(() => _isLoading = true);

    try {
      // Decode the image
      final image = img.decodeImage(_imageData!);
      if (image == null) return;

      // Apply filters
      img.Image processedImage = image;

      // Apply brightness
      if (_brightness != 0) {
        processedImage = img.adjustColor(
          processedImage,
          brightness: 1.0 + (_brightness / 100),
        );
      }

      // Apply contrast
      if (_contrast != 0) {
        processedImage = img.adjustColor(
          processedImage,
          contrast: 1.0 + (_contrast / 100),
        );
      }

      // Apply saturation
      if (_saturation != 0) {
        processedImage = img.adjustColor(
          processedImage,
          saturation: 1.0 + (_saturation / 100),
        );
      }

      // Encode back to bytes
      final processedBytes = Uint8List.fromList(img.encodeJpg(processedImage));

      setState(() {
        _processedImageData = processedBytes;
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('Error applying filters: $e');
      setState(() => _isLoading = false);
    }
  }

  Future<void> _saveEditedImage() async {
    if (_processedImageData == null) return;

    try {
      final tempDir = await getTemporaryDirectory();
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final editedPath = '${tempDir.path}/edited_image_$timestamp.jpg';

      final editedFile = File(editedPath);
      await editedFile.writeAsBytes(_processedImageData!);

      setState(() {
        _editedImageFile = editedFile;
      });
    } catch (e) {
      debugPrint('Error saving edited image: $e');
    }
  }

  void _resetToOriginal() {
    setState(() {
      _processedImageData = _imageData;
      _brightness = 0.0;
      _contrast = 0.0;
      _saturation = 0.0;
    });
  }

  void _finishEditing() async {
    await _saveEditedImage();
    if (mounted) {
      Navigator.of(context).pop(_editedImageFile ?? widget.imageFile);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
        title: Text(
          'Edit Image ${widget.imageIndex + 1}/${widget.totalImages}',
          style: const TextStyle(color: Colors.white),
        ),
        actions: [
          TextButton(
            onPressed: _finishEditing,
            child: const Text(
              'Done',
              style: TextStyle(color: Colors.blue, fontWeight: FontWeight.w600),
            ),
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // Image preview
                Expanded(
                  flex: 3,
                  child: Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(16),
                    child: _processedImageData != null
                        ? ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: Image.memory(
                              _processedImageData!,
                              fit: BoxFit.contain,
                              width: double.infinity,
                              height: double.infinity,
                            ),
                          )
                        : const Center(
                            child: Text(
                              'Failed to load image',
                              style: TextStyle(color: Colors.white),
                            ),
                          ),
                  ),
                ),

                // Controls
                Expanded(
                  flex: 2,
                  child: Container(
                    color: Colors.grey[900],
                    child: Column(
                      children: [
                        // Action buttons
                        Padding(
                          padding: const EdgeInsets.all(16),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            children: [
                              _buildActionButton(
                                icon: Icons.tune,
                                label: 'Filters',
                                onPressed: () {
                                  setState(() => _showFilters = !_showFilters);
                                },
                                isActive: _showFilters,
                              ),
                              _buildActionButton(
                                icon: Icons.refresh,
                                label: 'Reset',
                                onPressed: _resetToOriginal,
                              ),
                            ],
                          ),
                        ),

                        // Filter controls
                        if (_showFilters) ...[
                          const Divider(color: Colors.grey),
                          Expanded(
                            child: Padding(
                              padding: const EdgeInsets.all(16),
                              child: Column(
                                children: [
                                  _buildSlider(
                                    'Brightness',
                                    _brightness,
                                    -100,
                                    100,
                                    (value) {
                                      setState(() => _brightness = value);
                                      _applyFilters();
                                    },
                                  ),
                                  _buildSlider(
                                    'Contrast',
                                    _contrast,
                                    -100,
                                    100,
                                    (value) {
                                      setState(() => _contrast = value);
                                      _applyFilters();
                                    },
                                  ),
                                  _buildSlider(
                                    'Saturation',
                                    _saturation,
                                    -100,
                                    100,
                                    (value) {
                                      setState(() => _saturation = value);
                                      _applyFilters();
                                    },
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
              ],
            ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
    bool isLoading = false,
    bool isActive = false,
  }) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            color: isActive ? Colors.blue : Colors.grey[800],
            borderRadius: BorderRadius.circular(30),
          ),
          child: IconButton(
            onPressed: isLoading ? null : onPressed,
            icon: isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : Icon(icon, color: Colors.white),
          ),
        ),
        const SizedBox(height: 8),
        Text(label, style: const TextStyle(color: Colors.white, fontSize: 12)),
      ],
    );
  }

  Widget _buildSlider(
    String label,
    double value,
    double min,
    double max,
    ValueChanged<double> onChanged,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              label,
              style: const TextStyle(color: Colors.white, fontSize: 14),
            ),
            Text(
              value.round().toString(),
              style: const TextStyle(color: Colors.grey, fontSize: 12),
            ),
          ],
        ),
        Slider(
          value: value,
          min: min,
          max: max,
          divisions: 200,
          activeColor: Colors.blue,
          inactiveColor: Colors.grey[700],
          onChanged: onChanged,
        ),
      ],
    );
  }
}
