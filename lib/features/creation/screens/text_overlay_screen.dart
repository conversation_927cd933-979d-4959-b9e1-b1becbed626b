import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:path_provider/path_provider.dart';

class TextOverlayScreen extends StatefulWidget {
  final File imageFile;

  const TextOverlayScreen({super.key, required this.imageFile});

  @override
  State<TextOverlayScreen> createState() => _TextOverlayScreenState();
}

class _TextOverlayScreenState extends State<TextOverlayScreen> {
  final GlobalKey _repaintBoundaryKey = GlobalKey();
  final TextEditingController _textController = TextEditingController();

  final List<TextOverlay> _textOverlays = [];
  int? _selectedOverlayIndex;
  bool _isEditing = false;

  // Text styling options
  String _selectedFont = 'Roboto';
  double _fontSize = 24.0;
  Color _textColor = Colors.white;
  Color _backgroundColor = Colors.transparent;
  FontWeight _fontWeight = FontWeight.normal;
  TextAlign _textAlign = TextAlign.center;
  bool _hasShadow = true;

  final List<Color> _colorOptions = [
    Colors.white,
    Colors.black,
    Colors.red,
    Colors.blue,
    Colors.green,
    Colors.yellow,
    Colors.purple,
    Colors.orange,
    Colors.pink,
    Colors.cyan,
  ];

  @override
  void dispose() {
    _textController.dispose();
    super.dispose();
  }

  void _addTextOverlay() {
    if (_textController.text.trim().isEmpty) return;

    setState(() {
      _textOverlays.add(
        TextOverlay(
          text: _textController.text.trim(),
          position: const Offset(0.5, 0.5), // Center of screen
          fontSize: _fontSize,
          color: _textColor,
          backgroundColor: _backgroundColor,
          fontFamily: _selectedFont,
          fontWeight: _fontWeight,
          textAlign: _textAlign,
          hasShadow: _hasShadow,
        ),
      );
      _textController.clear();
      _isEditing = false;
    });
  }

  void _editTextOverlay(int index) {
    final overlay = _textOverlays[index];
    setState(() {
      _selectedOverlayIndex = index;
      _textController.text = overlay.text;
      _fontSize = overlay.fontSize;
      _textColor = overlay.color;
      _backgroundColor = overlay.backgroundColor;
      _selectedFont = overlay.fontFamily;
      _fontWeight = overlay.fontWeight;
      _textAlign = overlay.textAlign;
      _hasShadow = overlay.hasShadow;
      _isEditing = true;
    });
  }

  void _updateTextOverlay() {
    if (_selectedOverlayIndex == null || _textController.text.trim().isEmpty) {
      return;
    }

    setState(() {
      _textOverlays[_selectedOverlayIndex!] =
          _textOverlays[_selectedOverlayIndex!].copyWith(
            text: _textController.text.trim(),
            fontSize: _fontSize,
            color: _textColor,
            backgroundColor: _backgroundColor,
            fontFamily: _selectedFont,
            fontWeight: _fontWeight,
            textAlign: _textAlign,
            hasShadow: _hasShadow,
          );
      _textController.clear();
      _selectedOverlayIndex = null;
      _isEditing = false;
    });
  }

  Future<File?> _saveImageWithOverlays() async {
    try {
      final RenderRepaintBoundary boundary =
          _repaintBoundaryKey.currentContext!.findRenderObject()
              as RenderRepaintBoundary;

      final ui.Image image = await boundary.toImage(pixelRatio: 2.0);
      final ByteData? byteData = await image.toByteData(
        format: ui.ImageByteFormat.png,
      );

      if (byteData == null) return null;

      final tempDir = await getTemporaryDirectory();
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final filePath = '${tempDir.path}/text_overlay_$timestamp.png';

      final file = File(filePath);
      await file.writeAsBytes(byteData.buffer.asUint8List());

      return file;
    } catch (e) {
      debugPrint('Error saving image with overlays: $e');
      return null;
    }
  }

  void _finishEditing() async {
    final result = await _saveImageWithOverlays();
    if (mounted) {
      Navigator.of(context).pop(result);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
        title: const Text('Add Text'),
        actions: [
          TextButton(
            onPressed: _finishEditing,
            child: const Text(
              'Done',
              style: TextStyle(color: Colors.blue, fontWeight: FontWeight.w600),
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          // Image with text overlays
          Expanded(
            flex: 3,
            child: Container(
              width: double.infinity,
              color: Colors.black,
              child: RepaintBoundary(
                key: _repaintBoundaryKey,
                child: Stack(
                  children: [
                    // Background image
                    Positioned.fill(
                      child: Image.file(widget.imageFile, fit: BoxFit.contain),
                    ),
                    // Text overlays
                    ...List.generate(_textOverlays.length, (index) {
                      final overlay = _textOverlays[index];
                      return Positioned(
                        left:
                            overlay.position.dx *
                                MediaQuery.of(context).size.width -
                            100,
                        top:
                            overlay.position.dy *
                                MediaQuery.of(context).size.height -
                            50,
                        child: GestureDetector(
                          onTap: () => _editTextOverlay(index),
                          onPanUpdate: (details) {
                            setState(() {
                              final newX =
                                  (details.globalPosition.dx) /
                                  MediaQuery.of(context).size.width;
                              final newY =
                                  (details.globalPosition.dy - kToolbarHeight) /
                                  MediaQuery.of(context).size.height;
                              _textOverlays[index] = overlay.copyWith(
                                position: Offset(
                                  newX.clamp(0.0, 1.0),
                                  newY.clamp(0.0, 1.0),
                                ),
                              );
                            });
                          },
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: overlay.backgroundColor,
                              borderRadius: BorderRadius.circular(4),
                              border: _selectedOverlayIndex == index
                                  ? Border.all(color: Colors.blue, width: 2)
                                  : null,
                            ),
                            child: Text(
                              overlay.text,
                              style: TextStyle(
                                fontSize: overlay.fontSize,
                                color: overlay.color,
                                fontFamily: overlay.fontFamily,
                                fontWeight: overlay.fontWeight,
                                shadows: overlay.hasShadow
                                    ? [
                                        const Shadow(
                                          offset: Offset(1, 1),
                                          blurRadius: 2,
                                          color: Colors.black54,
                                        ),
                                      ]
                                    : null,
                              ),
                              textAlign: overlay.textAlign,
                            ),
                          ),
                        ),
                      );
                    }),
                  ],
                ),
              ),
            ),
          ),

          // Text editing controls
          Expanded(
            flex: 2,
            child: Container(
              color: Colors.grey[900],
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  // Text input
                  Row(
                    children: [
                      Expanded(
                        child: TextField(
                          controller: _textController,
                          style: const TextStyle(color: Colors.white),
                          decoration: InputDecoration(
                            hintText: 'Enter text...',
                            hintStyle: TextStyle(color: Colors.grey[400]),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                              borderSide: BorderSide(color: Colors.grey[600]!),
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                              borderSide: BorderSide(color: Colors.grey[600]!),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                              borderSide: const BorderSide(color: Colors.blue),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      ElevatedButton(
                        onPressed: _isEditing
                            ? _updateTextOverlay
                            : _addTextOverlay,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 20,
                            vertical: 12,
                          ),
                        ),
                        child: Text(
                          _isEditing ? 'Update' : 'Add',
                          style: const TextStyle(color: Colors.white),
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // Text styling options
                  Expanded(
                    child: SingleChildScrollView(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Font size slider
                          _buildSlider(
                            'Font Size',
                            _fontSize,
                            12,
                            48,
                            (value) => setState(() => _fontSize = value),
                          ),

                          const SizedBox(height: 16),

                          // Color options
                          const Text(
                            'Text Color',
                            style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Wrap(
                            spacing: 8,
                            children: _colorOptions.map((color) {
                              return GestureDetector(
                                onTap: () => setState(() => _textColor = color),
                                child: Container(
                                  width: 32,
                                  height: 32,
                                  decoration: BoxDecoration(
                                    color: color,
                                    shape: BoxShape.circle,
                                    border: Border.all(
                                      color: _textColor == color
                                          ? Colors.blue
                                          : Colors.grey,
                                      width: 2,
                                    ),
                                  ),
                                ),
                              );
                            }).toList(),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSlider(
    String label,
    double value,
    double min,
    double max,
    ValueChanged<double> onChanged,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '$label: ${value.round()}',
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.w600,
          ),
        ),
        Slider(
          value: value,
          min: min,
          max: max,
          divisions: (max - min).round(),
          activeColor: Colors.blue,
          inactiveColor: Colors.grey[600],
          onChanged: onChanged,
        ),
      ],
    );
  }
}

class TextOverlay {
  final String text;
  final Offset position;
  final double fontSize;
  final Color color;
  final Color backgroundColor;
  final String fontFamily;
  final FontWeight fontWeight;
  final TextAlign textAlign;
  final bool hasShadow;

  const TextOverlay({
    required this.text,
    required this.position,
    required this.fontSize,
    required this.color,
    required this.backgroundColor,
    required this.fontFamily,
    required this.fontWeight,
    required this.textAlign,
    required this.hasShadow,
  });

  TextOverlay copyWith({
    String? text,
    Offset? position,
    double? fontSize,
    Color? color,
    Color? backgroundColor,
    String? fontFamily,
    FontWeight? fontWeight,
    TextAlign? textAlign,
    bool? hasShadow,
  }) {
    return TextOverlay(
      text: text ?? this.text,
      position: position ?? this.position,
      fontSize: fontSize ?? this.fontSize,
      color: color ?? this.color,
      backgroundColor: backgroundColor ?? this.backgroundColor,
      fontFamily: fontFamily ?? this.fontFamily,
      fontWeight: fontWeight ?? this.fontWeight,
      textAlign: textAlign ?? this.textAlign,
      hasShadow: hasShadow ?? this.hasShadow,
    );
  }
}
