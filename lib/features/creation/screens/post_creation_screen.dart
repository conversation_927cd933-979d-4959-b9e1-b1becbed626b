import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:video_player/video_player.dart';
import 'package:billionaires_social/core/services/firebase_service.dart';
import 'package:billionaires_social/features/feed/models/post_model.dart';
import 'package:billionaires_social/features/feed/providers/feed_provider.dart';
import 'dart:io';

import 'package:billionaires_social/features/profile/models/profile_model.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:billionaires_social/core/services/analytics_service.dart';
import 'package:billionaires_social/core/service_locator.dart';
import 'package:billionaires_social/features/stories/widgets/music_selector.dart';
import 'package:billionaires_social/features/search/widgets/user_search_widget.dart';
import 'package:billionaires_social/features/creation/screens/image_editing_screen.dart';
import 'package:billionaires_social/features/profile/services/profile_service.dart';
import 'package:billionaires_social/features/profile/providers/profile_provider.dart';
import 'package:billionaires_social/core/services/enhanced_media_picker_service.dart';
import 'package:billionaires_social/core/services/universal_user_role_service.dart';
import 'package:billionaires_social/core/services/universal_content_service.dart';
import 'package:billionaires_social/features/creation/widgets/smart_mention_text_field.dart';

import 'package:font_awesome_flutter/font_awesome_flutter.dart';

class PostCreationScreen extends ConsumerStatefulWidget {
  final File? mediaFile;

  const PostCreationScreen({super.key, this.mediaFile});

  @override
  ConsumerState<PostCreationScreen> createState() => _PostCreationScreenState();
}

class _PostCreationScreenState extends ConsumerState<PostCreationScreen> {
  final TextEditingController _captionController = TextEditingController();
  final TextEditingController _locationController = TextEditingController();
  File? _selectedMedia;
  MediaType _mediaType = MediaType.image;
  bool _isLoading = false;
  VideoPlayerController? _videoController;
  ProfileModel? _invitedCollaborator;
  final List<MediaTag> _mediaTags = [];
  bool _isTaggingMode = false;

  // Enhanced multi-media support
  final List<MediaFile> _selectedMediaFiles = [];
  final List<String> _uploadedMediaUrls = [];
  final List<MediaType> _uploadedMediaTypes = [];

  // Role-based media selection
  int _mediaLimit = 5;
  String _privacy = 'public'; // public, followers, private, closeFriends
  List<File> _mediaFiles = [];
  ProfileModel? _currentUserProfile;
  double _uploadProgress = 0.0;
  bool _uploadFailed = false;

  // New features
  String? _selectedMusicPath;
  String? _selectedMusicTitle;
  bool _enableAILabel = false;
  bool _turnOffCommenting = false;
  bool _hideLikeCount = false;
  bool _allowSharing = true;
  DateTime? _scheduledDate;
  bool _boostPost = false;
  bool _enableCompression = false; // Disabled by default to avoid path issues

  @override
  void initState() {
    super.initState();
    _clearAllState(); // Clear state first to ensure clean slate
    _clearDraft(); // Clear any existing drafts to guarantee clean slate
    _fetchUserProfile();

    // Handle media file if provided (e.g., from camera/gallery)
    if (widget.mediaFile != null) {
      _selectedMedia = widget.mediaFile;
      _initializeMedia();
    }

    // Note: Draft restoration is removed from initState to ensure clean slate
    // Drafts should only be restored when explicitly requested by user
  }

  /// Clear all state variables to ensure clean slate for new post creation
  void _clearAllState() {
    _captionController.clear();
    _locationController.clear();
    _selectedMedia = null;
    _mediaType = MediaType.image;
    _isLoading = false;
    _videoController?.dispose();
    _videoController = null;
    _invitedCollaborator = null;
    _mediaTags.clear();
    _isTaggingMode = false;
    _mediaLimit = 5;
    _privacy = 'public';
    _mediaFiles.clear();
    _currentUserProfile = null;
    _uploadProgress = 0.0;
    _uploadFailed = false;
    _selectedMusicPath = null;
    _selectedMusicTitle = null;
    _enableAILabel = false;
    _turnOffCommenting = false;
    _hideLikeCount = false;
    _allowSharing = true;
    _scheduledDate = null;
    _boostPost = false;
    _enableCompression = false;
  }

  Future<void> _fetchUserProfile() async {
    try {
      // Universal profile fetching using ProfileService
      final profileService = getIt<ProfileService>();
      final profile = await profileService.getCurrentUserProfile();

      if (profile != null) {
        setState(() {
          _currentUserProfile = profile;
        });

        // Universal media limit based on account type
        await _setUniversalMediaLimit();
      } else {
        throw Exception('Unable to fetch user profile');
      }
    } catch (e) {
      debugPrint('❌ Error fetching user profile: $e');
      // Show error to user
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading profile: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Universal media limit setting based on account type
  Future<void> _setUniversalMediaLimit() async {
    final currentUserId = UniversalUserRoleService.getCurrentUserId();
    if (currentUserId == null) return;

    final limits = await UniversalUserRoleService.getContentLimits(
      currentUserId,
    );
    setState(() {
      _mediaLimit = limits.maxMediaPerPost == -1 ? 999 : limits.maxMediaPerPost;
    });

    debugPrint('📊 Universal media limit set: $_mediaLimit');
  }

  Future<void> _initializeMedia() async {
    if (_selectedMedia != null) {
      final path = _selectedMedia!.path;
      _mediaType = path.contains('.mp4') || path.contains('.mov')
          ? MediaType.video
          : MediaType.image;

      if (_mediaType == MediaType.video) {
        _videoController = VideoPlayerController.file(_selectedMedia!);
        await _videoController!.initialize();
        setState(() {});
      }
    }
  }

  @override
  void dispose() {
    // Only save draft if there's meaningful content and user hasn't posted
    // This prevents empty captions from persisting between sessions
    if ((_captionController.text.trim().isNotEmpty || _mediaFiles.isNotEmpty) &&
        !_isLoading) {
      _saveDraft();
    }
    _captionController.dispose();
    _locationController.dispose();
    _videoController?.dispose();
    super.dispose();
  }

  Future<File> _compressImage(File file) async {
    try {
      // Check if the original file exists
      if (!await file.exists()) {
        debugPrint('❌ Original file does not exist: ${file.path}');
        return file;
      }

      debugPrint('🔄 Starting image compression for: ${file.path}');

      // Get app's temporary directory for safe file operations
      final tempDir = Directory.systemTemp;
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final compressedPath = '${tempDir.path}/compressed_image_$timestamp.jpg';

      debugPrint('🔄 Compressing to: $compressedPath');

      // Use compressAndGetFile with a completely new path
      final result = await FlutterImageCompress.compressAndGetFile(
        file.absolute.path,
        compressedPath,
        quality: 70,
        minWidth: 800,
        minHeight: 600,
        format: CompressFormat.jpeg,
      );

      if (result != null) {
        final compressedFile = File(result.path);

        // Double-check the compressed file exists and has content
        if (await compressedFile.exists()) {
          final fileSize = await compressedFile.length();
          if (fileSize > 0) {
            debugPrint(
              '✅ Image compressed successfully: ${result.path} ($fileSize bytes)',
            );
            return compressedFile;
          } else {
            debugPrint('❌ Compressed file is empty, using original');
            return file;
          }
        } else {
          debugPrint('❌ Compressed file was not created, using original');
          return file;
        }
      } else {
        debugPrint('❌ Image compression returned null, using original file');
        return file;
      }
    } catch (e) {
      debugPrint('❌ Error compressing image: $e');
      // Always return original file if anything goes wrong
      return file;
    }
  }

  Future<void> _pickMultiplePhotos() async {
    // Capture context before async operations
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    // Remove any invalid media files and count only valid ones
    final validMedia = _mediaFiles.where((file) => file.existsSync()).toList();

    // Update the media files list to only include valid files
    if (validMedia.length != _mediaFiles.length) {
      setState(() {
        _mediaFiles = validMedia;
        if (_mediaFiles.isNotEmpty) {
          _selectedMedia = _mediaFiles.first;
        } else {
          _selectedMedia = null;
        }
      });
    }

    // Only show limit warning if user is actually trying to add beyond limit
    if (validMedia.length >= _mediaLimit && _mediaLimit > 0) {
      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text('You can only select up to $_mediaLimit media files.'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    try {
      final picker = ImagePicker();
      final pickedFiles = await picker.pickMultipleMedia(
        imageQuality: 80,
        requestFullMetadata: false,
      );

      if (pickedFiles.isNotEmpty && mounted) {
        for (final pickedFile in pickedFiles) {
          // Check if we've reached the limit
          if (_mediaFiles.length >= _mediaLimit && _mediaLimit > 0) {
            if (mounted) {
              scaffoldMessenger.showSnackBar(
                SnackBar(
                  content: Text(
                    'Reached maximum limit of $_mediaLimit photos. Some photos were not added.',
                  ),
                  backgroundColor: Colors.orange,
                ),
              );
            }
            break;
          }

          try {
            File file = File(pickedFile.path);

            // Validate file exists and is readable
            if (!await file.exists()) {
              debugPrint(
                '⚠️ Skipping file that no longer exists: ${pickedFile.path}',
              );
              continue;
            }

            // Check file size
            final fileSize = await file.length();
            if (fileSize == 0) {
              debugPrint('⚠️ Skipping empty file: ${pickedFile.path}');
              continue;
            }

            // Determine media type
            final isVideo = pickedFile.path.toLowerCase().contains(
              RegExp(r'\.(mp4|mov|avi|mkv)$'),
            );
            final mediaType = isVideo ? MediaType.video : MediaType.image;

            // Only compress images if compression is enabled and it's not a video
            if (mediaType == MediaType.image && _enableCompression) {
              try {
                debugPrint(
                  '🔄 Attempting image compression for ${pickedFile.path}...',
                );
                file = await _compressImage(file);

                // Validate compressed file
                if (!await file.exists() || (await file.length()) == 0) {
                  debugPrint(
                    '⚠️ Compressed file invalid or empty, using original',
                  );
                  file = File(pickedFile.path);
                }
              } catch (e) {
                debugPrint('⚠️ Image compression failed, using original: $e');
                file = File(pickedFile.path);
              }
            }

            // Final validation before adding to list
            if (await file.exists() && await file.length() > 0) {
              // For images, navigate to editing screen first
              if (mediaType == MediaType.image) {
                final editedFile = await _navigateToImageEditor(
                  file,
                  _mediaFiles.length,
                );
                if (editedFile != null) {
                  setState(() {
                    _mediaFiles.add(editedFile);
                    _selectedMedia ??= editedFile;
                    _mediaType = mediaType;
                  });
                  debugPrint('✅ Edited image added successfully');
                } else {
                  // User cancelled editing, add original file
                  setState(() {
                    _mediaFiles.add(file);
                    _selectedMedia ??= file;
                    _mediaType = mediaType;
                  });
                  debugPrint('✅ Original image added successfully');
                }
              } else {
                // For videos, add directly
                setState(() {
                  _mediaFiles.add(file);
                  _selectedMedia ??= file;
                  _mediaType = mediaType;
                });
                debugPrint('✅ Video added successfully');
              }
            }
          } catch (e) {
            debugPrint('❌ Error processing file ${pickedFile.path}: $e');
            // Continue with next file
          }
        }

        // Show success message
        if (_mediaFiles.isNotEmpty && mounted) {
          scaffoldMessenger.showSnackBar(
            SnackBar(
              content: Text(
                '${_mediaFiles.length} photos selected successfully!',
              ),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('❌ Error picking multiple photos: $e');
      if (mounted) {
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text('Error selecting photos: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _removePhoto(int index) async {
    if (index < 0 || index >= _mediaFiles.length) return;

    setState(() {
      final removedFile = _mediaFiles.removeAt(index);
      if (_selectedMedia == removedFile) {
        _selectedMedia = _mediaFiles.isNotEmpty ? _mediaFiles.first : null;
      }
    });

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Photo removed successfully!'),
          backgroundColor: Colors.orange,
        ),
      );
    }
  }

  Future<void> _pickMedia(ImageSource source) async {
    // Remove any invalid media files and count only valid ones
    final validMedia = _mediaFiles.where((file) => file.existsSync()).toList();

    // Update the media files list to only include valid files
    if (validMedia.length != _mediaFiles.length) {
      setState(() {
        _mediaFiles = validMedia;
        if (_mediaFiles.isNotEmpty) {
          _selectedMedia = _mediaFiles.first;
        } else {
          _selectedMedia = null;
        }
      });
    }

    // Only show limit warning if user is actually trying to add beyond limit
    if (validMedia.length >= _mediaLimit && _mediaLimit > 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('You can only select up to $_mediaLimit media files.'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }
    try {
      final picker = ImagePicker();
      final pickedFile = await picker.pickMedia(
        imageQuality: 80,
        requestFullMetadata: false,
      );
      if (pickedFile != null) {
        try {
          File file = File(pickedFile.path);

          // Validate file exists and is readable
          if (!await file.exists()) {
            throw Exception(
              'Selected file no longer exists or is inaccessible. Please try again or ensure the file is downloaded to your device.',
            );
          }

          // Check file size
          final fileSize = await file.length();
          if (fileSize == 0) {
            throw Exception(
              'Selected file is empty or could not be read. Please try selecting a different file.',
            );
          }

          debugPrint('📁 Selected file: ${file.path} ($fileSize bytes)');

          // Determine media type first
          final isVideo =
              pickedFile.path.toLowerCase().contains('.mp4') ||
              pickedFile.path.toLowerCase().contains('.mov') ||
              pickedFile.path.toLowerCase().contains('.avi') ||
              pickedFile.path.toLowerCase().contains('.mkv');

          final mediaType = isVideo ? MediaType.video : MediaType.image;

          // Only compress images if compression is enabled and it's not a video
          if (mediaType == MediaType.image && _enableCompression) {
            try {
              debugPrint('🔄 Attempting image compression...');
              file = await _compressImage(file);

              // Validate compressed file
              if (!await file.exists() || (await file.length()) == 0) {
                debugPrint(
                  '⚠️ Compressed file invalid or empty, using original',
                );
                file = File(pickedFile.path);
              }
            } catch (e) {
              debugPrint('⚠️ Image compression failed, using original: $e');
              file = File(pickedFile.path);
            }
          } else if (mediaType == MediaType.image) {
            debugPrint('📸 Using original image without compression');
          }

          // Final validation before adding to list
          if (await file.exists() && await file.length() > 0) {
            // For images, navigate to editing screen first
            if (mediaType == MediaType.image) {
              final editedFile = await _navigateToImageEditor(
                file,
                _mediaFiles.length,
              );
              if (editedFile != null) {
                setState(() {
                  _mediaFiles.add(editedFile);
                  _selectedMedia = editedFile;
                  _mediaType = mediaType;
                });
                debugPrint('✅ Edited image added successfully');
              } else {
                // User cancelled editing, add original file
                setState(() {
                  _mediaFiles.add(file);
                  _selectedMedia = file;
                  _mediaType = mediaType;
                });
                debugPrint('✅ Original image added successfully');
              }
            } else {
              // For videos, add directly
              setState(() {
                _mediaFiles.add(file);
                _selectedMedia = file;
                _mediaType = mediaType;
              });

              // Initialize video controller if it's a video
              if (_mediaType == MediaType.video) {
                _videoController?.dispose(); // Dispose previous controller
                _videoController = VideoPlayerController.file(_selectedMedia!);
                try {
                  await _videoController!.initialize();
                  setState(() {});
                } catch (e) {
                  debugPrint('⚠️ Video initialization failed: $e');
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('Failed to load video: $e')),
                    );
                  }
                }
              }
              debugPrint('✅ Media file added successfully');
            }
          } else {
            throw Exception('File validation failed after processing');
          }
        } catch (e) {
          debugPrint('❌ Error processing selected file: $e');
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Error loading file: ${e.toString()}'),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      }
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Error picking media: $e')));
    }
  }

  Future<void> _inviteCollaborator(BuildContext context) async {
    ProfileModel? selectedUser;
    await showDialog(
      context: context,
      builder: (context) {
        return Dialog(
          child: Container(
            width: MediaQuery.of(context).size.width * 0.9,
            height: MediaQuery.of(context).size.height * 0.7,
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'Invite Collaborator',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(Icons.close),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Expanded(
                  child: UserSearchWidget(
                    hintText: 'Search for collaborators...',
                    searchType: UserSearchType.collaboration,
                    verifiedOnly: true,
                    excludeUserIds: _currentUserProfile != null
                        ? [_currentUserProfile!.id]
                        : null,
                    onUserSelected: (user) {
                      selectedUser = user;
                      Navigator.pop(context);
                    },
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
    if (selectedUser != null) {
      setState(() {
        _invitedCollaborator = selectedUser;
      });
    }
  }

  // Parse mentions and hashtags from caption
  List<String> _extractMentions(String text) {
    final mentionRegex = RegExp(r'@(\w+)');
    return mentionRegex
        .allMatches(text)
        .map((match) => match.group(1)!)
        .toList();
  }

  List<String> _extractHashtags(String text) {
    final hashtagRegex = RegExp(r'#(\w+)');
    return hashtagRegex
        .allMatches(text)
        .map((match) => match.group(1)!)
        .toList();
  }

  // Add tag to media
  void _addMediaTag(TapDownDetails details, RenderBox renderBox) {
    if (!_isTaggingMode) return;

    final localPosition = renderBox.globalToLocal(details.globalPosition);
    final size = renderBox.size;

    // Convert to relative coordinates (0-1)
    final x = localPosition.dx / size.width;
    final y = localPosition.dy / size.height;

    // TODO: Show user selection dialog
    _showUserSelectionDialog(x, y);
  }

  /// Navigate to image editing screen
  Future<File?> _navigateToImageEditor(File imageFile, int imageIndex) async {
    try {
      final result = await Navigator.of(context).push<File>(
        MaterialPageRoute(
          builder: (context) => ImageEditingScreen(
            imageFile: imageFile,
            imageIndex: imageIndex,
            totalImages: _mediaFiles.length + 1, // +1 for current image
          ),
        ),
      );
      return result;
    } catch (e) {
      debugPrint('❌ Error navigating to image editor: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to open image editor: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
      return null;
    }
  }

  /// Edit an existing image in the media list
  Future<void> _editExistingImage(int index) async {
    if (index >= _mediaFiles.length) return;

    final originalFile = _mediaFiles[index];
    final editedFile = await _navigateToImageEditor(originalFile, index);

    if (editedFile != null && mounted) {
      setState(() {
        _mediaFiles[index] = editedFile;
        if (_selectedMedia == originalFile) {
          _selectedMedia = editedFile;
        }
      });

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Image updated successfully'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  void _showUserSelectionDialog(double x, double y) {
    showDialog(
      context: context,
      builder: (context) {
        return Dialog(
          child: Container(
            width: MediaQuery.of(context).size.width * 0.9,
            height: MediaQuery.of(context).size.height * 0.7,
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'Tag User',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(Icons.close),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Expanded(
                  child: UserSearchWidget(
                    hintText: 'Search users to tag...',
                    searchType: UserSearchType.tagging,
                    excludeUserIds: _currentUserProfile != null
                        ? [_currentUserProfile!.id]
                        : null,
                    onUserSelected: (user) {
                      final tag = MediaTag(
                        userId: user.id,
                        username: user.username,
                        x: x,
                        y: y,
                      );
                      setState(() {
                        _mediaTags.add(tag);
                        _isTaggingMode = false;
                      });
                      Navigator.pop(context);
                    },
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Future<String?> _uploadMediaWithProgress(File file, String fileName) async {
    try {
      // Verify file exists before upload
      if (!await file.exists()) {
        throw Exception('File does not exist: ${file.path}');
      }

      debugPrint('Starting upload for file: ${file.path}');
      debugPrint('File size: ${await file.length()} bytes');

      // Use FirebaseService for upload instead of direct Firebase Storage
      final firebaseService = ref.read(firebaseServiceProvider);

      // Check if user is authenticated
      if (firebaseService.currentUser == null) {
        throw Exception('User not authenticated for upload');
      }

      // Use the existing uploadImage method from FirebaseService
      final downloadUrl = await firebaseService.uploadImage(
        file.path,
        'posts/$fileName',
      );

      setState(() {
        _uploadProgress = 1.0; // Complete
        _uploadFailed = false;
      });

      debugPrint('Upload successful: $downloadUrl');
      return downloadUrl;
    } catch (e) {
      debugPrint('Upload failed: $e');
      setState(() {
        _uploadProgress = 0.0;
        _uploadFailed = true;
      });

      // Provide more specific error messages
      if (e.toString().contains('unauthorized')) {
        throw Exception(
          'Upload failed: You need to be logged in to upload media',
        );
      } else if (e.toString().contains('No such file')) {
        throw Exception('Upload failed: Selected file is no longer available');
      } else {
        throw Exception('Upload failed: ${e.toString()}');
      }
    }
  }

  Future<void> _createPost() async {
    // Check if we have either single media or multiple media
    if (_selectedMedia == null && _selectedMediaFiles.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please select an image or video')),
      );
      return;
    }

    setState(() {
      _isLoading = true;
      _uploadFailed = false;
      _uploadProgress = 0.0;
    });

    try {
      final firebaseService = ref.read(firebaseServiceProvider);
      final currentUser = firebaseService.currentUser;

      if (currentUser == null) {
        throw Exception(
          'User not authenticated. Please sign in and try again.',
        );
      }

      debugPrint('🔐 Auth: Current user ID: ${currentUser.uid}');
      debugPrint('🔐 Auth: User email: ${currentUser.email}');

      // Verify authentication token is still valid
      try {
        await currentUser.getIdToken(true);
        debugPrint('🔐 Auth: Token refresh successful');
      } catch (e) {
        debugPrint('❌ Auth: Token refresh failed: $e');
        throw Exception(
          'Authentication session expired. Please sign out and sign in again.',
        );
      }

      // Handle multiple media files or single media
      String primaryMediaUrl;
      MediaType primaryMediaType;

      if (_selectedMediaFiles.isNotEmpty) {
        // Upload multiple media files
        _uploadedMediaUrls.clear();
        _uploadedMediaTypes.clear();

        for (int i = 0; i < _selectedMediaFiles.length; i++) {
          final mediaFile = _selectedMediaFiles[i];

          // Verify file exists
          if (!await mediaFile.file.exists()) {
            throw Exception('Media file ${i + 1} is no longer available.');
          }

          // Upload file
          final fileExtension = mediaFile.file.path.split('.').last;
          final fileName =
              '${DateTime.now().millisecondsSinceEpoch}_${currentUser.uid}_$i.$fileExtension';

          debugPrint(
            'Uploading file ${i + 1}/${_selectedMediaFiles.length}: ${mediaFile.file.path}',
          );
          final mediaUrl = await _uploadMediaWithProgress(
            mediaFile.file,
            fileName,
          );

          if (mediaUrl == null || mediaUrl.isEmpty) {
            throw Exception('Media upload failed for file ${i + 1}');
          }

          _uploadedMediaUrls.add(mediaUrl);
          _uploadedMediaTypes.add(
            mediaFile.type == MediaFileType.video
                ? MediaType.video
                : MediaType.image,
          );

          debugPrint('Media ${i + 1} uploaded successfully: $mediaUrl');
        }

        // Use first media as primary
        primaryMediaUrl = _uploadedMediaUrls.first;
        primaryMediaType = _uploadedMediaTypes.first;
      } else {
        // Single media upload (legacy)
        if (!await _selectedMedia!.exists()) {
          throw Exception(
            'Selected media file is no longer available. Please select a new file.',
          );
        }

        final fileExtension = _selectedMedia!.path.split('.').last;
        final fileName =
            '${DateTime.now().millisecondsSinceEpoch}_${currentUser.uid}.$fileExtension';

        debugPrint('Uploading file: ${_selectedMedia!.path}');
        final mediaUrl = await _uploadMediaWithProgress(
          _selectedMedia!,
          fileName,
        );

        if (mediaUrl == null || mediaUrl.isEmpty) {
          throw Exception('Media upload failed - no URL returned');
        }

        primaryMediaUrl = mediaUrl;
        primaryMediaType = _mediaType;
        debugPrint('Media uploaded successfully: $mediaUrl');
      }

      // Parse mentions and hashtags
      final mentionedUsers = _extractMentions(_captionController.text);
      final hashtags = _extractHashtags(_captionController.text);

      // Create post using Universal Content Service
      final createdPost = await UniversalContentService.createPost(
        caption: _captionController.text.trim(),
        mediaUrl: primaryMediaUrl,
        mediaType: primaryMediaType,
        location: _locationController.text.trim().isEmpty
            ? null
            : _locationController.text.trim(),
        coAuthorIds: _invitedCollaborator != null
            ? [_invitedCollaborator!.id]
            : null,
        coAuthorUsernames: _invitedCollaborator != null
            ? [_invitedCollaborator!.username]
            : null,
        coAuthorAvatars: _invitedCollaborator != null
            ? [_invitedCollaborator!.profilePictureUrl]
            : null,
        mentionedUsers: mentionedUsers.isNotEmpty ? mentionedUsers : null,
        hashtags: hashtags.isNotEmpty ? hashtags : null,
        mediaTags: _mediaTags.isNotEmpty
            ? _mediaTags.map((tag) => tag.toString()).toList()
            : null,
        // Multiple media support
        mediaUrls: _uploadedMediaUrls.isNotEmpty ? _uploadedMediaUrls : null,
        mediaTypes: _uploadedMediaTypes.isNotEmpty ? _uploadedMediaTypes : null,
        visibility: _privacy,
      );

      if (createdPost == null) {
        throw Exception('Failed to create post');
      }

      // Log analytics event
      final analyticsService = getIt<AnalyticsService>();
      await analyticsService.logEventSafely(
        eventName: 'post_created',
        parameters: {
          'media_type': _mediaType.name,
          'has_location': _locationController.text.isNotEmpty,
          'user_role': _currentUserProfile?.userType ?? 'unknown',
          'privacy': _privacy,
        },
      );

      // Add the new post to the feed provider for immediate display
      debugPrint(
        '📝 POST CREATION: Post creation complete, adding to feed provider...',
      );

      // Ensure the feed provider is initialized first
      try {
        final currentFeedState = ref.read(feedProvider);
        debugPrint(
          '📊 POST CREATION: Current feed state: ${currentFeedState.valueOrNull?.length ?? 0} posts',
        );

        ref.read(feedProvider.notifier).addNewPost(createdPost);
        debugPrint('✅ POST CREATION: Post added to feed provider successfully');

        // Verify the post was added
        final updatedFeedState = ref.read(feedProvider);
        debugPrint(
          '📊 POST CREATION: Updated feed state: ${updatedFeedState.valueOrNull?.length ?? 0} posts',
        );
      } catch (e) {
        debugPrint('⚠️ POST CREATION: Error adding post to feed provider: $e');
        debugPrint('🔄 POST CREATION: Triggering feed refresh instead...');
        await ref.read(feedProvider.notifier).refresh();
      }

      // CRITICAL: Refresh profile providers to show new post in profile screen
      debugPrint('🔄 POST CREATION: Refreshing profile providers...');
      try {
        // Get current user ID
        final profileService = getIt<ProfileService>();
        final currentUserId = profileService.getCurrentUserId();

        if (currentUserId != null) {
          // Invalidate profile providers to force refresh
          ref.invalidate(userProfileProvider);
          ref.invalidate(profileProvider(currentUserId));
          ref.invalidate(
            userPostsWithPinnedStatusStreamProvider(currentUserId),
          );

          // Also refresh the user profile provider to update post count
          await ref.read(userProfileProvider.notifier).refresh();

          debugPrint(
            '✅ POST CREATION: Profile providers refreshed for user: $currentUserId',
          );

          // Add a small delay to ensure providers have time to refresh
          await Future.delayed(const Duration(milliseconds: 500));
        }
      } catch (e) {
        debugPrint('⚠️ POST CREATION: Error refreshing profile providers: $e');
      }

      // Clear draft after successful post creation
      await _clearDraft();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Post created successfully!'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error creating post: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Widget _buildPrivacySelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Who can see this post?',
          style: TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8.0,
          runSpacing: 8.0,
          children: [
            _buildPrivacyChip('Public', 'public', Icons.public),
            _buildPrivacyChip('Followers', 'followers', Icons.people),
            _buildPrivacyChip('Private', 'private', Icons.lock),
            _buildPrivacyChip('Close Friends', 'closeFriends', Icons.favorite),
          ],
        ),
      ],
    );
  }

  Widget _buildPrivacyChip(String label, String value, IconData icon) {
    final isSelected = _privacy == value;
    return GestureDetector(
      onTap: () => setState(() => _privacy = value),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: isSelected ? Colors.blue[900] : Colors.grey[900],
          borderRadius: BorderRadius.circular(25),
          border: Border.all(
            color: isSelected ? Colors.blue[700]! : Colors.grey[700]!,
            width: 1.5,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              color: isSelected ? Colors.blue[300] : Colors.grey[400],
              size: 16,
            ),
            const SizedBox(width: 8),
            Text(
              label,
              style: TextStyle(
                color: isSelected ? Colors.white : Colors.grey[300],
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _openMusicSelector() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) {
        return SizedBox(
          height: MediaQuery.of(context).size.height * 0.7,
          child: MusicSelector(
            selectedMusicPath: _selectedMusicPath,
            onMusicSelected: (musicPath) {
              setState(() {
                _selectedMusicPath = musicPath;
                _selectedMusicTitle =
                    musicPath?.split('/').last.split('.').first ?? '';
              });
            },
          ),
        );
      },
    );
  }

  void _openAdvancedSettings() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setModalState) {
            return Container(
              padding: const EdgeInsets.all(16),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Advanced Settings',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),

                  // AI Label Toggle
                  SwitchListTile(
                    title: const Text('Enable AI Label'),
                    subtitle: const Text('Show if AI was used in creation'),
                    value: _enableAILabel,
                    onChanged: (value) {
                      setState(() => _enableAILabel = value);
                      setModalState(() => _enableAILabel = value);
                    },
                  ),

                  // Comment Controls
                  SwitchListTile(
                    title: const Text('Turn off commenting'),
                    subtitle: const Text('Prevent users from commenting'),
                    value: _turnOffCommenting,
                    onChanged: (value) {
                      setState(() => _turnOffCommenting = value);
                      setModalState(() => _turnOffCommenting = value);
                    },
                  ),

                  // Hide Like Count
                  SwitchListTile(
                    title: const Text('Hide like count'),
                    subtitle: const Text('Hide the number of likes'),
                    value: _hideLikeCount,
                    onChanged: (value) {
                      setState(() => _hideLikeCount = value);
                      setModalState(() => _hideLikeCount = value);
                    },
                  ),

                  // Allow Sharing
                  SwitchListTile(
                    title: const Text('Allow sharing'),
                    subtitle: const Text('Let users share this post'),
                    value: _allowSharing,
                    onChanged: (value) {
                      setState(() => _allowSharing = value);
                      setModalState(() => _allowSharing = value);
                    },
                  ),

                  const Divider(),

                  // Schedule Post
                  ListTile(
                    leading: const Icon(Icons.schedule),
                    title: const Text('Schedule Post'),
                    subtitle: Text(
                      _scheduledDate != null
                          ? 'Scheduled for ${_scheduledDate!.toString().split('.')[0]}'
                          : 'Post immediately',
                    ),
                    trailing: TextButton(
                      onPressed: () => _selectScheduleDateTime(setModalState),
                      child: Text(_scheduledDate != null ? 'Change' : 'Set'),
                    ),
                  ),

                  // Image Compression
                  SwitchListTile(
                    title: const Text('Enable Image Compression'),
                    subtitle: const Text(
                      'Reduce image file size (may cause upload issues if enabled)',
                    ),
                    value: _enableCompression,
                    secondary: const Icon(Icons.compress),
                    onChanged: (value) {
                      setState(() => _enableCompression = value);
                      setModalState(() => _enableCompression = value);
                    },
                  ),

                  // Boost Post
                  SwitchListTile(
                    title: const Text('Boost Post'),
                    subtitle: const Text('Promote for wider reach'),
                    value: _boostPost,
                    secondary: const Icon(Icons.rocket_launch),
                    onChanged: (value) {
                      setState(() => _boostPost = value);
                      setModalState(() => _boostPost = value);
                    },
                  ),

                  const SizedBox(height: 16),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () => Navigator.pop(context),
                      child: const Text('Done'),
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  // Draft save/restore - Modified to be optional
  Future<void> _saveDraft() async {
    // Only save draft if user has actually entered content
    if (_captionController.text.trim().isNotEmpty || _mediaFiles.isNotEmpty) {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('draft_caption', _captionController.text);
      await prefs.setString('draft_privacy', _privacy);
      // Save media file paths (if any)
      final mediaPaths = _mediaFiles.map((f) => f.path).toList();
      await prefs.setStringList('draft_media', mediaPaths);
    }
  }

  /// Clear draft data
  Future<void> _clearDraft() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('draft_caption');
    await prefs.remove('draft_privacy');
    await prefs.remove('draft_media');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.close, color: Colors.white),
          onPressed: _isLoading ? null : () => Navigator.pop(context),
        ),
        title: const Text(
          'Create Post',
          style: TextStyle(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        actions: [
          Container(
            margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
            child: ElevatedButton(
              onPressed: _isLoading ? null : _createPost,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                elevation: 0,
                padding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 8,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: _isLoading
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Text(
                      'Share',
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 14,
                      ),
                    ),
            ),
          ),
        ],
      ),
      body: _isLoading
          ? const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('Creating your post...'),
                ],
              ),
            )
          : Container(
              color: Colors.black,
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (_isLoading && _uploadProgress > 0.0 && !_uploadFailed)
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8.0),
                        child: LinearProgressIndicator(value: _uploadProgress),
                      ),
                    if (_uploadFailed)
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8.0),
                        child: Column(
                          children: [
                            const Text('Upload failed. Please try again.'),
                            ElevatedButton(
                              onPressed: _isLoading ? null : _createPost,
                              child: const Text('Retry Upload'),
                            ),
                          ],
                        ),
                      ),
                    // Media Selection with Tagging
                    if (_mediaFiles.isNotEmpty) ...[
                      // Media counter and info
                      Padding(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.photo_library,
                              color: Colors.grey[400],
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              '${_mediaFiles.length} photo${_mediaFiles.length == 1 ? '' : 's'} selected',
                              style: TextStyle(
                                color: Colors.grey[400],
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            const Spacer(),
                            if (_mediaFiles.length > 1)
                              Text(
                                'Tap to edit • Long press to reorder',
                                style: TextStyle(
                                  color: Colors.grey[500],
                                  fontSize: 12,
                                ),
                              ),
                          ],
                        ),
                      ),
                      SizedBox(
                        height: 200,
                        child: ReorderableListView.builder(
                          scrollDirection: Axis.horizontal,
                          itemCount: _mediaFiles.length,
                          onReorder: (oldIndex, newIndex) {
                            setState(() {
                              if (newIndex > oldIndex) {
                                newIndex -= 1;
                              }
                              final item = _mediaFiles.removeAt(oldIndex);
                              _mediaFiles.insert(newIndex, item);
                            });
                          },
                          itemBuilder: (context, index) {
                            final file = _mediaFiles[index];
                            return Padding(
                              key: ValueKey('media_$index'),
                              padding: const EdgeInsets.all(8.0),
                              child: GestureDetector(
                                onTapDown: (details) {
                                  final renderBox =
                                      context.findRenderObject() as RenderBox?;
                                  if (renderBox != null) {
                                    _addMediaTag(details, renderBox);
                                  }
                                },
                                child: Stack(
                                  children: [
                                    file.path.endsWith('.mp4') ||
                                            file.path.endsWith('.mov')
                                        ? AspectRatio(
                                            aspectRatio: 16 / 9,
                                            child: VideoPlayer(
                                              VideoPlayerController.file(file),
                                            ),
                                          )
                                        : Image.file(file),
                                    // Edit button for images
                                    if (!file.path.endsWith('.mp4') &&
                                        !file.path.endsWith('.mov'))
                                      Positioned(
                                        top: 8,
                                        left: 8,
                                        child: Container(
                                          decoration: BoxDecoration(
                                            color: Colors.black.withValues(
                                              alpha: 0.6,
                                            ),
                                            borderRadius: BorderRadius.circular(
                                              20,
                                            ),
                                          ),
                                          child: IconButton(
                                            onPressed: () =>
                                                _editExistingImage(index),
                                            icon: const Icon(
                                              Icons.edit,
                                              color: Colors.white,
                                              size: 20,
                                            ),
                                            padding: const EdgeInsets.all(8),
                                            constraints: const BoxConstraints(
                                              minWidth: 36,
                                              minHeight: 36,
                                            ),
                                          ),
                                        ),
                                      ),
                                    // Remove button for all media
                                    Positioned(
                                      top: 8,
                                      right: 8,
                                      child: Container(
                                        decoration: BoxDecoration(
                                          color: Colors.red.withValues(
                                            alpha: 0.8,
                                          ),
                                          borderRadius: BorderRadius.circular(
                                            20,
                                          ),
                                        ),
                                        child: IconButton(
                                          onPressed: () => _removePhoto(index),
                                          icon: const Icon(
                                            Icons.close,
                                            color: Colors.white,
                                            size: 20,
                                          ),
                                          padding: const EdgeInsets.all(8),
                                          constraints: const BoxConstraints(
                                            minWidth: 36,
                                            minHeight: 36,
                                          ),
                                        ),
                                      ),
                                    ),

                                    // Show existing tags
                                    ..._mediaTags.map(
                                      (tag) => Positioned(
                                        left: tag.x * 200, // Approximate width
                                        top: tag.y * 200, // Approximate height
                                        child: Container(
                                          padding: const EdgeInsets.symmetric(
                                            horizontal: 8,
                                            vertical: 4,
                                          ),
                                          decoration: BoxDecoration(
                                            color: Colors.black54,
                                            borderRadius: BorderRadius.circular(
                                              12,
                                            ),
                                          ),
                                          child: Text(
                                            '@${tag.username}',
                                            style: const TextStyle(
                                              color: Colors.white,
                                              fontSize: 12,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                    Row(
                      children: [
                        Expanded(
                          child: Container(
                            height: 48,
                            decoration: BoxDecoration(
                              color: Colors.grey[900],
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(color: Colors.grey[700]!),
                            ),
                            child: InkWell(
                              onTap: () => _pickMedia(ImageSource.gallery),
                              borderRadius: BorderRadius.circular(12),
                              child: const Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.add_a_photo,
                                    color: Colors.white,
                                    size: 20,
                                  ),
                                  SizedBox(width: 8),
                                  Text(
                                    'Add Media',
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Container(
                            height: 48,
                            decoration: BoxDecoration(
                              color: Colors.purple[900],
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(color: Colors.purple[700]!),
                            ),
                            child: InkWell(
                              onTap: _pickMultiplePhotos,
                              borderRadius: BorderRadius.circular(12),
                              child: const Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.photo_library,
                                    color: Colors.white,
                                    size: 20,
                                  ),
                                  SizedBox(width: 8),
                                  Text(
                                    'Multiple',
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Container(
                          height: 48,
                          decoration: BoxDecoration(
                            color: _isTaggingMode
                                ? Colors.red[900]
                                : Colors.blue[900],
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: _isTaggingMode
                                  ? Colors.red[700]!
                                  : Colors.blue[700]!,
                            ),
                          ),
                          child: InkWell(
                            onTap: () {
                              setState(() {
                                _isTaggingMode = !_isTaggingMode;
                              });
                            },
                            borderRadius: BorderRadius.circular(12),
                            child: Padding(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 16,
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                    _isTaggingMode
                                        ? Icons.person_remove
                                        : Icons.person_add,
                                    color: Colors.white,
                                    size: 20,
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    _isTaggingMode ? 'Stop' : 'Tag',
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),

                    // Music and Advanced Options Row
                    Row(
                      children: [
                        Expanded(
                          child: Container(
                            height: 48,
                            decoration: BoxDecoration(
                              color: _selectedMusicPath != null
                                  ? Colors.green[900]
                                  : Colors.grey[900],
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: _selectedMusicPath != null
                                    ? Colors.green[700]!
                                    : Colors.grey[700]!,
                              ),
                            ),
                            child: InkWell(
                              onTap: _openMusicSelector,
                              borderRadius: BorderRadius.circular(12),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  FaIcon(
                                    FontAwesomeIcons.music,
                                    color: Colors.white,
                                    size: 16,
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    _selectedMusicPath != null
                                        ? 'Change Music'
                                        : 'Add Music',
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Container(
                          height: 48,
                          decoration: BoxDecoration(
                            color: Colors.grey[900],
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(color: Colors.grey[700]!),
                          ),
                          child: InkWell(
                            onTap: _openAdvancedSettings,
                            borderRadius: BorderRadius.circular(12),
                            child: const Padding(
                              padding: EdgeInsets.symmetric(horizontal: 16),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                    Icons.tune,
                                    color: Colors.white,
                                    size: 20,
                                  ),
                                  SizedBox(width: 8),
                                  Text(
                                    'Options',
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),

                    // Show selected music
                    if (_selectedMusicPath != null)
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.green.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.green),
                        ),
                        child: Row(
                          children: [
                            const Icon(
                              Icons.music_note,
                              color: Colors.green,
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                _selectedMusicTitle ?? 'Music Selected',
                                style: const TextStyle(
                                  color: Colors.green,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                            IconButton(
                              onPressed: () {
                                setState(() {
                                  _selectedMusicPath = null;
                                  _selectedMusicTitle = null;
                                });
                              },
                              icon: const Icon(
                                Icons.close,
                                color: Colors.green,
                                size: 18,
                              ),
                            ),
                          ],
                        ),
                      ),
                    if (_selectedMusicPath != null) const SizedBox(height: 16),

                    // Show active settings indicators
                    if (_enableAILabel ||
                        _turnOffCommenting ||
                        _hideLikeCount ||
                        !_allowSharing ||
                        _scheduledDate != null ||
                        _boostPost)
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.blue.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.blue),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'Active Settings:',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.blue,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Wrap(
                              spacing: 8,
                              runSpacing: 4,
                              children: [
                                if (_enableAILabel)
                                  _buildSettingChip(
                                    'AI Label',
                                    Icons.smart_toy,
                                  ),
                                if (_turnOffCommenting)
                                  _buildSettingChip(
                                    'No Comments',
                                    Icons.comments_disabled,
                                  ),
                                if (_hideLikeCount)
                                  _buildSettingChip(
                                    'Hidden Likes',
                                    Icons.visibility_off,
                                  ),
                                if (!_allowSharing)
                                  _buildSettingChip('No Sharing', Icons.share),
                                if (_scheduledDate != null)
                                  _buildSettingChip(
                                    'Scheduled',
                                    Icons.schedule,
                                  ),
                                if (_boostPost)
                                  _buildSettingChip(
                                    'Boosted',
                                    Icons.rocket_launch,
                                  ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    if (_enableAILabel ||
                        _turnOffCommenting ||
                        _hideLikeCount ||
                        !_allowSharing ||
                        _scheduledDate != null ||
                        _boostPost)
                      const SizedBox(height: 16),

                    // Privacy selector
                    _buildPrivacySelector(),
                    const SizedBox(height: 16),
                    // Caption Input with mention/hashtag highlighting
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.grey[900],
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.grey[700]!),
                      ),
                      child: SmartMentionTextField(
                        controller: _captionController,
                        maxLines: 4,
                        hintText:
                            'Write a caption... Use @ to mention people, # for hashtags',
                        style: const TextStyle(color: Colors.white),
                        decoration: const InputDecoration(
                          hintStyle: TextStyle(color: Colors.grey),
                          border: InputBorder.none,
                          contentPadding: EdgeInsets.all(16),
                        ),
                        onMentionsChanged: (mentions) {
                          // Update mentions list when they change
                          setState(() {
                            // The mentions will be extracted in _createPost method
                          });
                        },
                        onHashtagsChanged: (hashtags) {
                          // Update hashtags list when they change
                          setState(() {
                            // The hashtags will be extracted in _createPost method
                          });
                        },
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Location Input
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.grey[900],
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.grey[700]!),
                      ),
                      child: TextField(
                        controller: _locationController,
                        style: const TextStyle(color: Colors.white),
                        decoration: const InputDecoration(
                          hintText: 'Add location',
                          hintStyle: TextStyle(color: Colors.grey),
                          prefixIcon: Icon(
                            Icons.location_on,
                            color: Colors.grey,
                          ),
                          border: InputBorder.none,
                          contentPadding: EdgeInsets.all(16),
                        ),
                      ),
                    ),
                    const SizedBox(height: 24),

                    // Remax (co-author) invitation
                    Wrap(
                      alignment: WrapAlignment.start,
                      crossAxisAlignment: WrapCrossAlignment.center,
                      spacing: 8.0,
                      runSpacing: 8.0,
                      children: [
                        ElevatedButton.icon(
                          onPressed: _isLoading
                              ? null
                              : () => _inviteCollaborator(context),
                          icon: const Icon(Icons.person_add),
                          label: const Text('Invite collaborator'),
                        ),
                        if (_invitedCollaborator != null) ...[
                          const SizedBox(width: 4),
                          CircleAvatar(
                            backgroundImage:
                                _invitedCollaborator!
                                    .profilePictureUrl
                                    .isNotEmpty
                                ? NetworkImage(
                                    _invitedCollaborator!.profilePictureUrl,
                                  )
                                : null,
                            child:
                                _invitedCollaborator!.profilePictureUrl.isEmpty
                                ? const Icon(Icons.person)
                                : null,
                          ),
                          const SizedBox(width: 4),
                          Flexible(
                            child: Text(
                              _invitedCollaborator!.username,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          IconButton(
                            icon: const Icon(Icons.close),
                            onPressed: _isLoading
                                ? null
                                : () => setState(
                                    () => _invitedCollaborator = null,
                                  ),
                          ),
                        ],
                      ],
                    ),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildSettingChip(String label, IconData icon) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.blue.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: Colors.blue),
          const SizedBox(width: 4),
          Text(
            label,
            style: const TextStyle(
              fontSize: 12,
              color: Colors.blue,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _selectScheduleDateTime(StateSetter setModalState) async {
    if (!mounted) return;

    final date = await showDatePicker(
      context: context,
      initialDate: DateTime.now().add(const Duration(hours: 1)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 30)),
    );

    if (date != null && mounted) {
      final time = await showTimePicker(
        context: context,
        initialTime: TimeOfDay.now(),
      );

      if (time != null && mounted) {
        setState(() {
          _scheduledDate = DateTime(
            date.year,
            date.month,
            date.day,
            time.hour,
            time.minute,
          );
        });
        setModalState(() {});
      }
    }
  }
}
