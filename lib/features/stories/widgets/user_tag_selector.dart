import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:billionaires_social/features/profile/models/profile_model.dart';

import 'package:cached_network_image/cached_network_image.dart';

class UserTagSelector extends ConsumerStatefulWidget {
  final List<String> selectedUserIds;
  final Function(List<String>) onUsersSelected;

  const UserTagSelector({
    super.key,
    required this.selectedUserIds,
    required this.onUsersSelected,
  });

  @override
  ConsumerState<UserTagSelector> createState() => _UserTagSelectorState();
}

class _UserTagSelectorState extends ConsumerState<UserTagSelector> {
  final TextEditingController _searchController = TextEditingController();
  List<ProfileModel> _searchResults = [];
  bool _isSearching = false;

  @override
  void initState() {
    super.initState();
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    if (_searchController.text.length >= 2) {
      _searchUsers(_searchController.text);
    } else {
      setState(() {
        _searchResults = [];
      });
    }
  }

  Future<void> _searchUsers(String query) async {
    setState(() {
      _isSearching = true;
    });

    try {
      // For now, we'll use mock data since searchUsers doesn't exist
      // In a real app, you'd implement this in ProfileService
      await Future.delayed(
        const Duration(milliseconds: 500),
      ); // Simulate API call

      final mockResults = [
        ProfileModel(
          id: '1',
          username: 'john_doe',
          name: 'John Doe',
          profilePictureUrl: 'https://via.placeholder.com/150',
          bio: 'Luxury lifestyle enthusiast',
          postCount: 150,
          followerCount: 5000,
          followingCount: 200,
        ),
        ProfileModel(
          id: '2',
          username: 'jane_smith',
          name: 'Jane Smith',
          profilePictureUrl: 'https://via.placeholder.com/150',
          bio: 'Business mogul',
          postCount: 89,
          followerCount: 12000,
          followingCount: 150,
        ),
        ProfileModel(
          id: '3',
          username: 'mike_wilson',
          name: 'Mike Wilson',
          profilePictureUrl: 'https://via.placeholder.com/150',
          bio: 'Tech entrepreneur',
          postCount: 234,
          followerCount: 8000,
          followingCount: 300,
        ),
      ];

      // Filter based on query
      final filteredResults = mockResults
          .where(
            (user) =>
                user.name.toLowerCase().contains(query.toLowerCase()) ||
                user.username.toLowerCase().contains(query.toLowerCase()),
          )
          .toList();

      setState(() {
        _searchResults = filteredResults;
      });
    } catch (e) {
      debugPrint('Error searching users: $e');
    } finally {
      setState(() {
        _isSearching = false;
      });
    }
  }

  void _toggleUserSelection(String userId) {
    final currentSelection = List<String>.from(widget.selectedUserIds);

    if (currentSelection.contains(userId)) {
      currentSelection.remove(userId);
    } else {
      currentSelection.add(userId);
    }

    widget.onUsersSelected(currentSelection);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 300,
      decoration: BoxDecoration(
        color: Colors.black87,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: const BoxDecoration(
              border: Border(
                bottom: BorderSide(color: Colors.white24, width: 0.5),
              ),
            ),
            child: Row(
              children: [
                const Text(
                  'Tag People',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Text(
                  '${widget.selectedUserIds.length} selected',
                  style: const TextStyle(color: Colors.white70, fontSize: 14),
                ),
              ],
            ),
          ),

          // Search bar
          Padding(
            padding: const EdgeInsets.all(16),
            child: TextField(
              controller: _searchController,
              style: const TextStyle(color: Colors.white),
              decoration: InputDecoration(
                hintText: 'Search users to tag...',
                hintStyle: const TextStyle(color: Colors.white54),
                prefixIcon: const Icon(Icons.search, color: Colors.white54),
                filled: true,
                fillColor: Colors.white10,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide.none,
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
            ),
          ),

          // Search results
          Expanded(
            child: _isSearching
                ? const Center(
                    child: CircularProgressIndicator(color: Colors.white),
                  )
                : _searchResults.isEmpty
                ? const Center(
                    child: Text(
                      'Search for users to tag',
                      style: TextStyle(color: Colors.white54),
                    ),
                  )
                : ListView.builder(
                    itemCount: _searchResults.length,
                    itemBuilder: (context, index) {
                      final user = _searchResults[index];
                      final isSelected = widget.selectedUserIds.contains(
                        user.id,
                      );

                      return ListTile(
                        leading: CircleAvatar(
                          radius: 20,
                          backgroundImage: CachedNetworkImageProvider(
                            user.profilePictureUrl,
                          ),
                        ),
                        title: Text(
                          user.name,
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        subtitle: Text(
                          '@${user.username}',
                          style: const TextStyle(color: Colors.white70),
                        ),
                        trailing: Container(
                          width: 24,
                          height: 24,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: isSelected
                                ? Colors.blue
                                : Colors.transparent,
                            border: Border.all(
                              color: isSelected ? Colors.blue : Colors.white54,
                              width: 2,
                            ),
                          ),
                          child: isSelected
                              ? const Icon(
                                  Icons.check,
                                  color: Colors.white,
                                  size: 16,
                                )
                              : null,
                        ),
                        onTap: () => _toggleUserSelection(user.id),
                      );
                    },
                  ),
          ),

          // Selected users chips
          if (widget.selectedUserIds.isNotEmpty)
            Container(
              padding: const EdgeInsets.all(16),
              child: Wrap(
                spacing: 8,
                runSpacing: 8,
                children: widget.selectedUserIds.map((userId) {
                  final user = _searchResults.firstWhere(
                    (u) => u.id == userId,
                    orElse: () => ProfileModel(
                      id: userId,
                      name: 'Unknown User',
                      username: 'unknown',
                      profilePictureUrl: '',
                      bio: '',
                      postCount: 0,
                      followerCount: 0,
                      followingCount: 0,
                    ),
                  );

                  return Chip(
                    avatar: CircleAvatar(
                      radius: 12,
                      backgroundImage: CachedNetworkImageProvider(
                        user.profilePictureUrl,
                      ),
                    ),
                    label: Text(
                      user.name,
                      style: const TextStyle(color: Colors.white),
                    ),
                    deleteIcon: const Icon(
                      Icons.close,
                      color: Colors.white,
                      size: 18,
                    ),
                    onDeleted: () => _toggleUserSelection(userId),
                    backgroundColor: Colors.blue.withValues(alpha: 0.3),
                  );
                }).toList(),
              ),
            ),
        ],
      ),
    );
  }
}
