import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:billionaires_social/features/stories/models/story_reel_model.dart';
import 'package:billionaires_social/features/stories/screens/story_viewer_screen.dart';
import 'package:billionaires_social/core/widgets/billionaire_badge.dart';

/// Circular carousel widget for displaying stories in a luxurious circular layout
class CircularStoryCarousel extends ConsumerStatefulWidget {
  final List<StoryReel> storyReels;
  final String? centerAvatarUrl;
  final String? centerUsername;
  final VoidCallback? onCenterTap;
  final double radius;
  final double itemSize;
  final int maxVisibleItems; // Limit visible items for performance
  final bool showNavigationDots; // Show dots for many stories
  final bool enableFastScroll; // Enable fast scroll for many stories

  const CircularStoryCarousel({
    super.key,
    required this.storyReels,
    this.centerAvatarUrl,
    this.centerUsername,
    this.onCenterTap,
    this.radius = 120.0,
    this.itemSize = 60.0,
    this.maxVisibleItems = 8, // Show max 8 stories at once
    this.showNavigationDots = true,
    this.enableFastScroll = true,
  });

  @override
  ConsumerState<CircularStoryCarousel> createState() =>
      _CircularStoryCarouselState();
}

class _CircularStoryCarouselState extends ConsumerState<CircularStoryCarousel>
    with TickerProviderStateMixin {
  late AnimationController _rotationController;
  late AnimationController _scaleController;
  late Animation<double> _rotationAnimation;
  late Animation<double> _scaleAnimation;

  double _currentRotation = 0.0;
  int _selectedIndex = 0;
  int _currentPage = 0; // For pagination with many stories
  bool _isDragging = false;

  // Get visible stories for current page
  List<StoryReel> get _visibleStories {
    final totalStories = widget.storyReels.length;
    if (totalStories <= widget.maxVisibleItems) {
      return widget.storyReels;
    }

    final startIndex = _currentPage * widget.maxVisibleItems;
    final endIndex = math.min(
      startIndex + widget.maxVisibleItems,
      totalStories,
    );
    return widget.storyReels.sublist(startIndex, endIndex);
  }

  // Get total pages
  int get _totalPages {
    return (widget.storyReels.length / widget.maxVisibleItems).ceil();
  }

  @override
  void initState() {
    super.initState();

    _rotationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _rotationAnimation = Tween<double>(begin: 0.0, end: 0.0).animate(
      CurvedAnimation(parent: _rotationController, curve: Curves.easeOutCubic),
    );

    _scaleAnimation = Tween<double>(begin: 1.0, end: 1.2).animate(
      CurvedAnimation(parent: _scaleController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _rotationController.dispose();
    _scaleController.dispose();
    super.dispose();
  }

  void _onPanStart(DragStartDetails details) {
    _isDragging = true;
    _rotationController.stop();
  }

  void _onPanUpdate(DragUpdateDetails details) {
    if (!_isDragging) return;

    final center = Offset(widget.radius, widget.radius);
    final angle = math.atan2(
      details.localPosition.dy - center.dy,
      details.localPosition.dx - center.dx,
    );

    setState(() {
      _currentRotation = angle;
    });

    // Update selected index based on rotation (use visible stories)
    final visibleStories = _visibleStories;
    if (visibleStories.isNotEmpty) {
      final normalizedAngle = (_currentRotation + math.pi * 2) % (math.pi * 2);
      final newIndex =
          ((normalizedAngle / (math.pi * 2)) * visibleStories.length).round() %
          visibleStories.length;

      if (newIndex != _selectedIndex) {
        setState(() {
          _selectedIndex = newIndex;
        });
        _triggerHapticFeedback();
      }
    }
  }

  void _onPanEnd(DragEndDetails details) {
    _isDragging = false;
    _snapToNearestItem();
  }

  void _snapToNearestItem() {
    final visibleStories = _visibleStories;
    if (visibleStories.isEmpty) return;

    final itemCount = visibleStories.length;
    final targetAngle = (_selectedIndex * 2 * math.pi) / itemCount;

    _rotationAnimation =
        Tween<double>(begin: _currentRotation, end: targetAngle).animate(
          CurvedAnimation(
            parent: _rotationController,
            curve: Curves.easeOutCubic,
          ),
        );

    _rotationController.forward(from: 0.0).then((_) {
      setState(() {
        _currentRotation = targetAngle;
      });
    });
  }

  // Navigate to next page of stories
  void _nextPage() {
    if (_currentPage < _totalPages - 1) {
      setState(() {
        _currentPage++;
        _selectedIndex = 0;
        _currentRotation = 0.0;
      });
      _triggerHapticFeedback();
    }
  }

  // Navigate to previous page of stories
  void _previousPage() {
    if (_currentPage > 0) {
      setState(() {
        _currentPage--;
        _selectedIndex = 0;
        _currentRotation = 0.0;
      });
      _triggerHapticFeedback();
    }
  }

  void _triggerHapticFeedback() {
    // Add haptic feedback for premium feel
    // HapticFeedback.lightImpact();
  }

  void _onStoryTap(StoryReel reel, int index) {
    // Scale animation for tap feedback
    _scaleController.forward().then((_) {
      _scaleController.reverse();
    });

    // Navigate to story viewer
    Navigator.of(context).push(
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            StoryViewerScreen(
              reel: reel,
              allReels: widget.storyReels,
              initialReelIndex: index,
            ),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return FadeTransition(opacity: animation, child: child);
        },
        transitionDuration: const Duration(milliseconds: 300),
      ),
    );
  }

  Color _getStoryBorderColor(StoryReel reel) {
    // Determine border color based on story type and status
    if (reel.isAllViewed) {
      return Colors.grey.withValues(alpha: 0.5);
    }

    if (reel.isCloseFriend) {
      return const Color(0xFF00D4AA); // Close friends green
    }

    // VIP/Premium users get gradient border effect
    if (reel.username.contains('vip') || reel.username.contains('premium')) {
      return const Color(0xFFFF6B6B); // VIP red
    }

    // Verified/billionaire users get golden border
    if (reel.username.contains('verified') ||
        reel.username.contains('billionaire')) {
      return const Color(0xFFD4AF37); // Golden border
    }

    // Default public story border
    return const Color(0xFF4ECDC4); // Teal for public stories
  }

  Widget _buildStoryItem(StoryReel reel, int index, double angle) {
    final isSelected = index == _selectedIndex;
    final x = widget.radius + (widget.radius * 0.8) * math.cos(angle);
    final y = widget.radius + (widget.radius * 0.8) * math.sin(angle);

    return Positioned(
      left: x - widget.itemSize / 2,
      top: y - widget.itemSize / 2,
      child: GestureDetector(
        onTap: () => _onStoryTap(reel, index),
        child: AnimatedBuilder(
          animation: _scaleAnimation,
          builder: (context, child) {
            final scale = isSelected ? _scaleAnimation.value : 1.0;

            return Transform.scale(
              scale: scale,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Story circle
                  Container(
                    width: widget.itemSize,
                    height: widget.itemSize,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: _getStoryBorderColor(reel),
                        width: isSelected ? 4.0 : 3.0,
                      ),
                      boxShadow: isSelected
                          ? [
                              BoxShadow(
                                color: _getStoryBorderColor(
                                  reel,
                                ).withValues(alpha: 0.6),
                                blurRadius: 12.0,
                                spreadRadius: 2.0,
                              ),
                            ]
                          : null,
                    ),
                    child: ClipOval(
                      child: Stack(
                        children: [
                          // Profile image
                          CachedNetworkImage(
                            imageUrl: reel.userAvatarUrl,
                            fit: BoxFit.cover,
                            width: widget.itemSize,
                            height: widget.itemSize,
                            placeholder: (context, url) => Container(
                              color: Colors.grey[300],
                              child: const Icon(
                                Icons.person,
                                color: Colors.white,
                              ),
                            ),
                            errorWidget: (context, url, error) => Container(
                              color: Colors.grey[300],
                              child: const Icon(
                                Icons.person,
                                color: Colors.white,
                              ),
                            ),
                          ),

                          // Billionaire badge for verified users
                          if (reel.username.contains(
                            'verified',
                          )) // Placeholder logic
                            const Positioned(
                              bottom: 0,
                              right: 0,
                              child: BillionaireBadge(size: 16),
                            ),
                        ],
                      ),
                    ),
                  ),

                  // Username below the circle
                  const SizedBox(height: 4),
                  SizedBox(
                    width: widget.itemSize + 10, // Slightly wider than circle
                    child: Text(
                      reel.username,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.w500,
                      ),
                      textAlign: TextAlign.center,
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildCenterAvatar() {
    return Positioned(
      left: widget.radius - 40,
      top: widget.radius - 40,
      child: GestureDetector(
        onTap: widget.onCenterTap,
        child: Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            border: Border.all(color: const Color(0xFFD4AF37), width: 3.0),
            boxShadow: [
              BoxShadow(
                color: const Color(0xFFD4AF37).withValues(alpha: 0.3),
                blurRadius: 16.0,
                spreadRadius: 4.0,
              ),
            ],
          ),
          child: ClipOval(
            child: widget.centerAvatarUrl != null
                ? CachedNetworkImage(
                    imageUrl: widget.centerAvatarUrl!,
                    fit: BoxFit.cover,
                    placeholder: (context, url) => Container(
                      color: Colors.grey[300],
                      child: const Icon(Icons.person, color: Colors.white),
                    ),
                  )
                : Container(
                    decoration: const BoxDecoration(
                      gradient: LinearGradient(
                        colors: [Color(0xFFD4AF37), Color(0xFFFFD700)],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                    ),
                    child: const Icon(Icons.add, color: Colors.white, size: 32),
                  ),
          ),
        ),
      ),
    );
  }

  // Build story count indicator
  Widget _buildStoryCountIndicator() {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.7),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        '${_currentPage * widget.maxVisibleItems + 1}-${math.min((_currentPage + 1) * widget.maxVisibleItems, widget.storyReels.length)} of ${widget.storyReels.length}',
        style: const TextStyle(
          color: Colors.white,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  // Build navigation arrows
  Widget _buildNavigationArrows() {
    return Stack(
      children: [
        // Left arrow
        if (_currentPage > 0)
          Positioned(
            left: 10,
            top: widget.radius - 15,
            child: GestureDetector(
              onTap: _previousPage,
              child: Container(
                width: 30,
                height: 30,
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.7),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.chevron_left,
                  color: Colors.white,
                  size: 20,
                ),
              ),
            ),
          ),

        // Right arrow
        if (_currentPage < _totalPages - 1)
          Positioned(
            right: 10,
            top: widget.radius - 15,
            child: GestureDetector(
              onTap: _nextPage,
              child: Container(
                width: 30,
                height: 30,
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.7),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.chevron_right,
                  color: Colors.white,
                  size: 20,
                ),
              ),
            ),
          ),
      ],
    );
  }

  // Build page dots
  Widget _buildPageDots() {
    return Container(
      margin: const EdgeInsets.only(top: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: List.generate(_totalPages, (index) {
          return GestureDetector(
            onTap: () => _goToPage(index),
            child: Container(
              width: 8,
              height: 8,
              margin: const EdgeInsets.symmetric(horizontal: 4),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: index == _currentPage
                    ? const Color(0xFFD4AF37)
                    : Colors.white.withValues(alpha: 0.5),
              ),
            ),
          );
        }),
      ),
    );
  }

  // Add fast scroll method
  void _goToPage(int page) {
    if (page >= 0 && page < _totalPages && page != _currentPage) {
      setState(() {
        _currentPage = page;
        _selectedIndex = 0;
        _currentRotation = 0.0;
      });
      _triggerHapticFeedback();
    }
  }

  @override
  Widget build(BuildContext context) {
    final visibleStories = _visibleStories;

    if (widget.storyReels.isEmpty) {
      return SizedBox(
        width: widget.radius * 2,
        height: widget.radius * 2,
        child: _buildCenterAvatar(),
      );
    }

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Story count indicator for many stories
        if (widget.storyReels.length > widget.maxVisibleItems)
          _buildStoryCountIndicator(),

        // Main circular carousel
        GestureDetector(
          onPanStart: _onPanStart,
          onPanUpdate: _onPanUpdate,
          onPanEnd: _onPanEnd,
          child: SizedBox(
            width: widget.radius * 2,
            height: widget.radius * 2,
            child: AnimatedBuilder(
              animation: _rotationAnimation,
              builder: (context, child) {
                final rotation = _isDragging
                    ? _currentRotation
                    : _rotationAnimation.value;

                return Stack(
                  children: [
                    // Story items (only visible ones)
                    ...visibleStories.asMap().entries.map((entry) {
                      final index = entry.key;
                      final reel = entry.value;
                      final angle =
                          rotation +
                          (index * 2 * math.pi) / visibleStories.length;

                      return _buildStoryItem(reel, index, angle);
                    }),

                    // Center avatar
                    _buildCenterAvatar(),

                    // Navigation arrows for many stories
                    if (widget.storyReels.length > widget.maxVisibleItems)
                      _buildNavigationArrows(),
                  ],
                );
              },
            ),
          ),
        ),

        // Page dots for many stories
        if (widget.showNavigationDots && _totalPages > 1) _buildPageDots(),
      ],
    );
  }
}
