import 'package:cloud_firestore/cloud_firestore.dart';

import 'package:flutter/foundation.dart';
import 'package:billionaires_social/features/messaging/services/chat_service.dart';
import 'package:billionaires_social/features/messaging/models/chat_model.dart';
import 'package:billionaires_social/features/notifications/services/notification_service.dart';
import 'package:billionaires_social/features/search/services/user_search_service.dart';
import 'package:billionaires_social/core/service_locator.dart';

/// Service to handle story mentions functionality
class StoryMentionService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// Process mentions in a story and send notifications
  static Future<void> processMentions({
    required String storyId,
    required List<String> mentionedUsernames,
    required String storyOwnerId,
    required String storyOwnerName,
  }) async {
    if (mentionedUsernames.isEmpty) return;

    try {
      debugPrint(
        '📱 Processing ${mentionedUsernames.length} mentions for story $storyId',
      );

      // Get user search service to find mentioned users
      final userSearchService = UserSearchService();

      for (final username in mentionedUsernames) {
        try {
          // Find user by username
          final users = await userSearchService.searchUsers(
            query: username,
            limit: 1,
          );

          if (users.isNotEmpty) {
            final mentionedUser = users.first;

            // Don't mention yourself
            if (mentionedUser.id == storyOwnerId) continue;

            // Send mention notification
            await _sendMentionNotification(
              storyId: storyId,
              storyOwnerId: storyOwnerId,
              storyOwnerName: storyOwnerName,
              mentionedUserId: mentionedUser.id,
              mentionedUsername: mentionedUser.username,
            );

            // Create chat for mention reply
            await _createMentionChat(
              storyId: storyId,
              storyOwnerId: storyOwnerId,
              storyOwnerName: storyOwnerName,
              mentionedUserId: mentionedUser.id,
              mentionedUsername: mentionedUser.username,
            );

            debugPrint(
              '✅ Processed mention for @$username (${mentionedUser.id})',
            );
          } else {
            debugPrint('⚠️ User not found for mention: @$username');
          }
        } catch (e) {
          debugPrint('❌ Error processing mention @$username: $e');
        }
      }
    } catch (e) {
      debugPrint('❌ Error processing story mentions: $e');
    }
  }

  /// Send mention notification
  static Future<void> _sendMentionNotification({
    required String storyId,
    required String storyOwnerId,
    required String storyOwnerName,
    required String mentionedUserId,
    required String mentionedUsername,
  }) async {
    try {
      // Create notification document
      await _firestore.collection('notifications').add({
        'userId': mentionedUserId,
        'type': 'story_mention',
        'title': 'You were mentioned in a story',
        'body': '$storyOwnerName mentioned you in their story',
        'fromUserId': storyOwnerId,
        'fromUserName': storyOwnerName,
        'storyId': storyId,
        'timestamp': FieldValue.serverTimestamp(),
        'isRead': false,
        'data': {
          'type': 'story_mention',
          'storyId': storyId,
          'storyOwnerId': storyOwnerId,
          'mentionedUserId': mentionedUserId,
        },
      });

      debugPrint('📱 Mention notification sent to $mentionedUsername');
    } catch (e) {
      debugPrint('❌ Error sending mention notification: $e');
    }
  }

  /// Create or get existing chat for mention reply
  static Future<void> _createMentionChat({
    required String storyId,
    required String storyOwnerId,
    required String storyOwnerName,
    required String mentionedUserId,
    required String mentionedUsername,
  }) async {
    try {
      final chatService = getIt<ChatService>();

      // Check if chat already exists between these users
      final existingChatId = await chatService.getOrCreateDirectChat(
        mentionedUserId,
        mentionedUsername,
        '', // avatar URL - will be fetched by the service
      );

      // Send initial mention message
      await chatService.sendMessage(
        chatId: existingChatId,
        content: '👋 You were mentioned in my story! Tap to reply.',
        type: MessageType.text,
      );

      // Store mention context in chat metadata
      await _firestore.collection('chats').doc(existingChatId).update({
        'lastMentionStoryId': storyId,
        'lastMentionTimestamp': FieldValue.serverTimestamp(),
      });

      debugPrint('💬 Mention chat created/updated: $existingChatId');
    } catch (e) {
      debugPrint('❌ Error creating mention chat: $e');
    }
  }

  /// Handle mention reply from notification
  static Future<void> handleMentionReply({
    required String storyId,
    required String replyMessage,
    required String replierId,
    required String replierName,
  }) async {
    try {
      // Get story owner
      final storyDoc = await _firestore
          .collection('stories')
          .doc(storyId)
          .get();
      if (!storyDoc.exists) return;

      final storyData = storyDoc.data()!;
      final storyOwnerId = storyData['userId'];

      if (storyOwnerId == replierId) return; // Don't reply to yourself

      // Get or create chat
      final chatService = getIt<ChatService>();
      final chatId = await chatService.getOrCreateDirectChat(
        storyOwnerId,
        storyData['userName'] ?? 'User',
        '', // avatar URL - will be fetched by the service
      );

      // Send reply message with story context
      await chatService.sendMessage(
        chatId: chatId,
        content: '📖 Reply to story mention: $replyMessage',
        type: MessageType.text,
      );

      // Send notification to story owner
      await _firestore.collection('notifications').add({
        'userId': storyOwnerId,
        'type': 'story_mention_reply',
        'title': 'Reply to your story mention',
        'body': '$replierName replied to your story mention',
        'fromUserId': replierId,
        'fromUserName': replierName,
        'storyId': storyId,
        'chatId': chatId,
        'timestamp': FieldValue.serverTimestamp(),
        'isRead': false,
        'data': {
          'type': 'story_mention_reply',
          'storyId': storyId,
          'chatId': chatId,
          'replierId': replierId,
        },
      });

      debugPrint('✅ Mention reply processed: $replyMessage');
    } catch (e) {
      debugPrint('❌ Error handling mention reply: $e');
    }
  }

  /// Extract mentions from text
  static List<String> extractMentions(String text) {
    final mentionRegex = RegExp(r'@(\w+)');
    return mentionRegex
        .allMatches(text)
        .map((match) => match.group(1)!)
        .toList();
  }

  /// Check if user can be mentioned (privacy settings)
  static Future<bool> canMentionUser(String userId) async {
    try {
      final userDoc = await _firestore.collection('users').doc(userId).get();
      if (!userDoc.exists) return false;

      final userData = userDoc.data()!;
      final privacySettings = userData['privacySettings'] ?? {};

      // Check if user allows mentions
      return privacySettings['allowMentions'] ?? true;
    } catch (e) {
      debugPrint('❌ Error checking mention permissions: $e');
      return false;
    }
  }
}
