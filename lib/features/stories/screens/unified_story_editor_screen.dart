import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/shared/story_shared_models.dart';
import '../providers/unified_story_provider.dart';
import '../providers/story_provider.dart' as story_provider;

class UnifiedStoryEditorScreen extends ConsumerStatefulWidget {
  final File mediaFile;
  const UnifiedStoryEditorScreen({super.key, required this.mediaFile});

  @override
  ConsumerState<UnifiedStoryEditorScreen> createState() =>
      _UnifiedStoryEditorScreenState();
}

class _UnifiedStoryEditorScreenState
    extends ConsumerState<UnifiedStoryEditorScreen>
    with TickerProviderStateMixin {
  late final StoryCreationNotifier _notifier;
  late final AnimationController _toolbarAnimationController;
  late final Animation<double> _toolbarAnimation;

  @override
  void initState() {
    super.initState();
    _notifier = ref.read(storyCreationProvider.notifier);
    _initializeAnimations();

    // Defer the media setting to avoid modifying provider during build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _notifier.setMedia(
        widget.mediaFile,
        StoryMediaType.image,
      ); // Default, can be changed
    });
  }

  void _initializeAnimations() {
    _toolbarAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _toolbarAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _toolbarAnimationController,
        curve: Curves.easeInOut,
      ),
    );
  }

  @override
  void dispose() {
    _toolbarAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(storyCreationProvider);
    return Scaffold(
      appBar: AppBar(
        title: const Text('Create Story'),
        actions: [
          IconButton(
            icon: const Icon(Icons.check),
            onPressed: state.isLoading
                ? null
                : () async {
                    final navigator = Navigator.of(context);
                    final scaffoldMessenger = ScaffoldMessenger.of(context);
                    final success = await _notifier.createStory();
                    if (success && mounted) {
                      // Refresh stories after successful creation
                      ref
                          .read(story_provider.storyReelsProvider.notifier)
                          .refresh();
                      navigator.pop(true);
                    } else if (state.error != null && mounted) {
                      scaffoldMessenger.showSnackBar(
                        SnackBar(
                          content: Text(state.error!),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  },
          ),
        ],
      ),
      body: Stack(
        children: [
          // Media preview
          Positioned.fill(
            child: Image.file(widget.mediaFile, fit: BoxFit.cover),
          ),
          // Drawing overlay
          if (state.drawingPoints.isNotEmpty)
            Positioned.fill(
              child: CustomPaint(
                painter: _DrawingPainter(
                  points: state.drawingPoints,
                  color: state.drawingColor ?? Colors.white,
                  width: state.drawingWidth ?? 3.0,
                ),
              ),
            ),
          // Text overlays
          ...state.textElements.map(
            (e) => Positioned(
              left: e.position.dx,
              top: e.position.dy,
              child: GestureDetector(
                onPanUpdate: (details) {
                  final idx = state.textElements.indexOf(e);
                  _notifier.updateTextElement(
                    idx,
                    e.copyWith(position: e.position + details.delta),
                  );
                },
                child: Container(
                  color: e.backgroundColor,
                  child: Text(
                    e.text,
                    style: TextStyle(
                      color: e.color,
                      fontSize: e.size,
                      fontFamily: e.fontFamily,
                      fontWeight: e.fontWeight,
                      fontStyle: e.isItalic
                          ? FontStyle.italic
                          : FontStyle.normal,
                    ),
                    textAlign: e.textAlign,
                  ),
                ),
              ),
            ),
          ),
          // UI toolbars and controls
          _buildToolbar(context, state),
          if (state.isLoading) const Center(child: CircularProgressIndicator()),
        ],
      ),
    );
  }

  Widget _buildToolbar(BuildContext context, StoryCreationState state) {
    return Align(
      alignment: Alignment.bottomCenter,
      child: FadeTransition(
        opacity: _toolbarAnimation,
        child: Container(
          color: Colors.black.withValues(alpha: 0.5),
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButton(
                  icon: const Icon(Icons.text_fields),
                  onPressed: () => _showTextDialog(context),
                ),
                IconButton(
                  icon: const Icon(Icons.brush),
                  onPressed: () => _showDrawingDialog(context),
                ),
                IconButton(
                  icon: const Icon(Icons.tag),
                  onPressed: () => _showTagDialog(context),
                ),
                IconButton(
                  icon: const Icon(Icons.music_note),
                  onPressed: () => _showMusicDialog(context),
                ),
                IconButton(
                  icon: const Icon(Icons.location_on),
                  onPressed: () => _showLocationDialog(context),
                ),
                IconButton(
                  icon: const Icon(Icons.filter),
                  onPressed: () => _showFilterDialog(context),
                ),
                IconButton(
                  icon: const Icon(Icons.privacy_tip),
                  onPressed: () => _showPrivacyDialog(context),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showTextDialog(BuildContext context) {
    final controller = TextEditingController();
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add Text'),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(hintText: 'Enter text'),
        ),
        actions: [
          TextButton(
            onPressed: () {
              if (controller.text.isNotEmpty) {
                _notifier.addTextElement(
                  TextElement(
                    text: controller.text,
                    color: Colors.white,
                    size: 24.0,
                    position: const Offset(100, 100),
                    backgroundColor: Colors.transparent,
                  ),
                );
              }
              Navigator.of(context).pop();
            },
            child: const Text('Add'),
          ),
        ],
      ),
    );
  }

  void _showDrawingDialog(BuildContext context) {
    // TODO: Implement drawing UI
  }

  void _showTagDialog(BuildContext context) {
    // TODO: Implement tagging UI
  }

  void _showMusicDialog(BuildContext context) {
    // TODO: Implement music selection UI
  }

  void _showLocationDialog(BuildContext context) {
    // TODO: Implement location selection UI
  }

  void _showFilterDialog(BuildContext context) {
    // TODO: Implement filter selection UI
  }

  void _showPrivacyDialog(BuildContext context) {
    // TODO: Implement privacy selection UI
  }
}

class _DrawingPainter extends CustomPainter {
  final List<DrawingPoint> points;
  final Color color;
  final double width;
  _DrawingPainter({
    required this.points,
    required this.color,
    required this.width,
  });
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = width
      ..strokeCap = StrokeCap.round
      ..strokeJoin = StrokeJoin.round;
    for (final point in points) {
      canvas.drawCircle(point.position, width / 2, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
