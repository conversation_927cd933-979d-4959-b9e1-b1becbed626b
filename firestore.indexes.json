{"indexes": [{"collectionGroup": "posts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "createdAt", "order": "DESCENDING"}, {"fieldPath": "userId", "order": "ASCENDING"}]}, {"collectionGroup": "posts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "events", "queryScope": "COLLECTION", "fields": [{"fieldPath": "dateTime", "order": "ASCENDING"}, {"fieldPath": "hostId", "order": "ASCENDING"}]}, {"collectionGroup": "stories", "queryScope": "COLLECTION", "fields": [{"fieldPath": "createdAt", "order": "DESCENDING"}, {"fieldPath": "userId", "order": "ASCENDING"}]}, {"collectionGroup": "posts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "isArchived", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "posts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "trending", "order": "ASCENDING"}, {"fieldPath": "trendingScore", "order": "DESCENDING"}]}, {"collectionGroup": "follows", "queryScope": "COLLECTION", "fields": [{"fieldPath": "followerId", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "followedAt", "order": "DESCENDING"}]}, {"collectionGroup": "follows", "queryScope": "COLLECTION", "fields": [{"fieldPath": "followingId", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "followedAt", "order": "DESCENDING"}]}, {"collectionGroup": "follows", "queryScope": "COLLECTION", "fields": [{"fieldPath": "followerId", "order": "ASCENDING"}, {"fieldPath": "followingId", "order": "ASCENDING"}]}], "fieldOverrides": []}